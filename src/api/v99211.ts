import {
  Activity99211CreateOrUpdateRequest,
  Activity99211CreateOrUpdateResponse,
  Activity99211QuestionRequest,
  Activity99211TemplateResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 骁龙能量活动
 * @summary 创建活动
 * @request POST:/99211/createActivity
 */
export const createActivity = (
  request: Activity99211CreateOrUpdateRequest,
): Promise<Activity99211CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99211/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 骁龙能量活动
 * @summary 查询活动信息
 * @request POST:/99211/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/99211/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 骁龙能量活动
 * @summary 下载答题模板
 * @request POST:/99211/quesTemplate/export
 */
export const quesTemplateExport = (): Promise<void> => {
  return httpRequest({
    url: '/99211/quesTemplate/export',
    method: 'post',
  });
};

/**
 * @tags 骁龙能量活动
 * @summary 导入答题模板
 * @request POST:/99211/quesTemplate/import
 */
export const quesTemplateImport = (file: any): Promise<Activity99211QuestionRequest[]> => {
  return httpRequest({
    url: '/99211/quesTemplate/import',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 骁龙能量活动
 * @summary 下载模板
 * @request POST:/99211/template/export
 */
export const templateExport = (): Promise<void> => {
  return httpRequest({
    url: '/99211/template/export',
    method: 'post',
  });
};

/**
 * @tags 骁龙能量活动
 * @summary 导入模板
 * @request POST:/99211/template/import
 */
export const templateImport = (file: any): Promise<Activity99211TemplateResponse[]> => {
  return httpRequest({
    url: '/99211/template/import',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 骁龙能量活动
 * @summary 修改活动
 * @request POST:/99211/updateActivity
 */
export const updateActivity = (
  request: Activity99211CreateOrUpdateRequest,
): Promise<Activity99211CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99211/updateActivity',
    method: 'post',
    data: request,
  });
};
