import {
  ExportReportUsingPost4Params,
  GetReportColumnUsingPostParams,
  IPageLinkedHashMapStringObject,
  LegendsandyMemberDataColumnVO,
  LegendsandyMemberDataRequest,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 蓝氏会员中心B端数据看板
 * @summary exportReport
 * @request POST:/legendsandy/data/report/export
 */
export const dataReportExport = (query: ExportReportUsingPost4Params): Promise<void> => {
  return httpRequest({
    url: '/legendsandy/data/report/export',
    method: 'post',
    params: query,
  });
};

/**
 * @tags 蓝氏会员中心B端数据看板
 * @summary getReportColumn
 * @request POST:/legendsandy/data/reportColumn
 */
export const dataReportColumn = (query: GetReportColumnUsingPostParams): Promise<LegendsandyMemberDataColumnVO[]> => {
  return httpRequest({
    url: '/legendsandy/data/reportColumn',
    method: 'post',
    params: query,
  });
};

/**
 * @tags 蓝氏会员中心B端数据看板
 * @summary getReportPage
 * @request POST:/legendsandy/data/reportPage
 */
export const dataReportPage = (request: LegendsandyMemberDataRequest): Promise<IPageLinkedHashMapStringObject> => {
  return httpRequest({
    url: '/legendsandy/data/reportPage',
    method: 'post',
    data: request,
  });
};
