import {
  GetActDetailsPageReqVO,
  GetActivityDataReqVO,
  GetActivitySumDataRespVO,
  GetActivityTypeRespVO,
  GetDataTrendsRespVO,
  IPageGetActivityDetailsPageRespVO,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 活动数据总览
 * @summary 活动详情数据
 * @request POST:/overview/getActivityDetailsPage
 */
export const getActivityDetailsPage = (reqVO: GetActDetailsPageReqVO): Promise<IPageGetActivityDetailsPageRespVO> => {
  return httpRequest({
    url: '/overview/getActivityDetailsPage',
    method: 'post',
    data: reqVO,
  });
};

/**
 * @tags 活动数据总览
 * @summary 根据类型获取活动累积数据
 * @request POST:/overview/getActivitySumDataByType
 */
export const getActivitySumDataByType = (reqVO: GetActivityDataReqVO): Promise<GetActivitySumDataRespVO[]> => {
  return httpRequest({
    url: '/overview/getActivitySumDataByType',
    method: 'post',
    data: reqVO,
  });
};

/**
 * @tags 活动数据总览
 * @summary 获取活动类型
 * @request GET:/overview/getActivityType
 */
export const getActivityType = (): Promise<GetActivityTypeRespVO[]> => {
  return httpRequest({
    url: '/overview/getActivityType',
    method: 'get',
  });
};

/**
 * @tags 活动数据总览
 * @summary 数据趋势
 * @request POST:/overview/getDataTrends
 */
export const getDataTrends = (reqVO: GetActivityDataReqVO): Promise<GetDataTrendsRespVO[]> => {
  return httpRequest({
    url: '/overview/getDataTrends',
    method: 'post',
    data: reqVO,
  });
};
