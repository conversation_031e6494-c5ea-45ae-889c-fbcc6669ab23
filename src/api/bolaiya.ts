import { BoLaiYaResultVo, GetZphyJsonUsingPostParams } from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 智铺会员珀莱雅看板
 * @summary 获取珀莱雅看板数据
 * @request POST:/bolaiya/getBoLaiYaData
 */
export const getBoLaiYaData = (query: GetZphyJsonUsingPostParams): Promise<BoLaiYaResultVo> => {
  return httpRequest({
    url: '/bolaiya/getBoLaiYaData',
    method: 'post',
    params: query,
  });
};
