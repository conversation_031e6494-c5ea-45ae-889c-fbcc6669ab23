import {
  Activity80020ActivityDataRequest,
  Activity80020CreateOrUpdateRequest,
  Activity80020CreateOrUpdateResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageActivity80020UserDrawLogResponse,
  IPageActivity80020UserGiveLogResponse,
  IPageActivity80020UserWinningLogResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 大转盘抽奖
 * @summary 创建活动
 * @request POST:/80020/createActivity
 */
export const createActivity = (
  request: Activity80020CreateOrUpdateRequest,
): Promise<Activity80020CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/80020/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 抽奖机会赠送记录
 * @request POST:/80020/data/giveLog
 */
export const dataGiveLog = (
  activity80020ActivityDataRequest: Activity80020ActivityDataRequest,
): Promise<IPageActivity80020UserGiveLogResponse> => {
  return httpRequest({
    url: '/80020/data/giveLog',
    method: 'post',
    data: activity80020ActivityDataRequest,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary  抽奖机会赠送记录导出
 * @request POST:/80020/data/giveLog/export
 */
export const dataGiveLogExport = (userDrawLogRequest80020: Activity80020ActivityDataRequest): Promise<void> => {
  return httpRequest({
    url: '/80020/data/giveLog/export',
    method: 'post',
    data: userDrawLogRequest80020,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 上传人群包
 * @request POST:/80020/data/lottery/uploadPin
 */
export const dataLotteryUploadPin = (userDrawLogRequest80020: Activity80020ActivityDataRequest): Promise<void> => {
  return httpRequest({
    url: '/80020/data/lottery/uploadPin',
    method: 'post',
    data: userDrawLogRequest80020,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 抽奖记录
 * @request POST:/80020/data/lotteryLog
 */
export const dataLotteryLog = (
  activity80020ActivityDataRequest: Activity80020ActivityDataRequest,
): Promise<IPageActivity80020UserDrawLogResponse> => {
  return httpRequest({
    url: '/80020/data/lotteryLog',
    method: 'post',
    data: activity80020ActivityDataRequest,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 抽奖记录导出
 * @request POST:/80020/data/lotteryLog/export
 */
export const dataLotteryLogExport = (
  activity80020ActivityDataRequest: Activity80020ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/80020/data/lotteryLog/export',
    method: 'post',
    data: activity80020ActivityDataRequest,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 上传人群包
 * @request POST:/80020/data/winning/uploadPin
 */
export const dataWinningUploadPin = (
  activity80020ActivityDataRequest: Activity80020ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/80020/data/winning/uploadPin',
    method: 'post',
    data: activity80020ActivityDataRequest,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 中奖记录
 * @request POST:/80020/data/winningLog
 */
export const dataWinningLog = (
  activity80020ActivityDataRequest: Activity80020ActivityDataRequest,
): Promise<IPageActivity80020UserWinningLogResponse> => {
  return httpRequest({
    url: '/80020/data/winningLog',
    method: 'post',
    data: activity80020ActivityDataRequest,
  });
};

/**
 * @tags 大转盘抽奖数据
 * @summary 中奖记录导出
 * @request POST:/80020/data/winningLog/export
 */
export const dataWinningLogExport = (
  activity80020ActivityDataRequest: Activity80020ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/80020/data/winningLog/export',
    method: 'post',
    data: activity80020ActivityDataRequest,
  });
};

/**
 * @tags 大转盘抽奖
 * @summary 查询活动信息
 * @request POST:/80020/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/80020/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 大转盘抽奖
 * @summary 修改活动
 * @request POST:/80020/updateActivity
 */
export const updateActivity = (
  request: Activity80020CreateOrUpdateRequest,
): Promise<Activity80020CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/80020/updateActivity',
    method: 'post',
    data: request,
  });
};
