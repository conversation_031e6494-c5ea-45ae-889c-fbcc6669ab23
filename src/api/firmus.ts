import {
  ActivityMcpd1000003568Response,
  ActivityMcpd1000003568SubmitRequest,
  ExportTurnDataUsingPostParams,
  FirmusCollectBottleQuery,
  FirmusMemberBabyInfoRequest,
  FirmusMemberCoinsAllResponse,
  FirmusMemberCoinsRequest,
  FirmusMemberDataRequest,
  FirmusMemberDataResponse,
  FirmusMemberGmvRequest,
  FirmusMemberGradeResponse,
  FirmusMemberLevelGmvDetailBizResponse,
  FirmusMemberLevelGmvDetailRequest,
  FirmusMemberLevelGmvForBigDataBizResponse,
  FirmusMemberLevelGmvForBigDataRequest,
  FirmusMemberLevelGmvRequest,
  FirmusMemberPointsRequest,
  FirmusMemberPointsResponse,
  FirmusMemberReceiveBizResponse,
  FirmusMemberReceiveRequest,
  FirmusMemberUpgradeRequest,
  FirmusNewMemberAccessGmvResponse,
  FirmusNewMemberGmvResponse,
  FirmusPopTurnDataResponse,
  FirmusPotActivityInfoDto,
  GetTurnDataUsingPostParams,
  IPageFirmusCollectBottleDto,
  IPageFirmusExchangeInfoDto,
  IPageFirmusMemberBabyInfoResponse,
  IPageFirmusMemberDataResponse,
  IPageFirmusMemberLevelGmvResponse,
  IPageFirmusOrderInfoDto,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 飞鹤集罐活动报表
 * @summary 导出活动数据报表
 * @request POST:/firmus/collect/bottle/data/export
 */
export const collectBottleDataExport = (query: FirmusCollectBottleQuery): Promise<void> => {
  return httpRequest({
    url: '/firmus/collect/bottle/data/export',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 飞鹤集罐活动报表
 * @summary 获取该活动下的子id
 * @request POST:/firmus/collect/bottle/data/getChildActId
 */
export const collectBottleDataGetChildActId = (
  request: FirmusCollectBottleQuery,
): Promise<FirmusPotActivityInfoDto[]> => {
  return httpRequest({
    url: '/firmus/collect/bottle/data/getChildActId',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 飞鹤集罐活动报表
 * @summary 分页查询活动数据报表
 * @request POST:/firmus/collect/bottle/data/query
 */
export const collectBottleDataQuery = (query: FirmusCollectBottleQuery): Promise<IPageFirmusCollectBottleDto> => {
  return httpRequest({
    url: '/firmus/collect/bottle/data/query',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 飞鹤集罐活动报表
 * @summary 查询用户领奖明细
 * @request POST:/firmus/collect/bottle/data/queryExchangeInfo
 */
export const collectBottleDataQueryExchangeInfo = (
  request: FirmusCollectBottleQuery,
): Promise<IPageFirmusExchangeInfoDto> => {
  return httpRequest({
    url: '/firmus/collect/bottle/data/queryExchangeInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 飞鹤集罐活动报表
 * @summary 查询用户领奖明细导出
 * @request POST:/firmus/collect/bottle/data/queryExchangeInfo/export
 */
export const collectBottleDataQueryExchangeInfoExport = (request: FirmusCollectBottleQuery): Promise<void> => {
  return httpRequest({
    url: '/firmus/collect/bottle/data/queryExchangeInfo/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 飞鹤集罐活动报表
 * @summary 查询活动订单数据
 * @request POST:/firmus/collect/bottle/data/queryOrderInfo
 */
export const collectBottleDataQueryOrderInfo = (
  request: FirmusCollectBottleQuery,
): Promise<IPageFirmusOrderInfoDto> => {
  return httpRequest({
    url: '/firmus/collect/bottle/data/queryOrderInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 飞鹤集罐活动报表
 * @summary 查询活动订单数据导出
 * @request POST:/firmus/collect/bottle/data/queryOrderInfo/export
 */
export const collectBottleDataQueryOrderInfoExport = (request: FirmusCollectBottleQuery): Promise<void> => {
  return httpRequest({
    url: '/firmus/collect/bottle/data/queryOrderInfo/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 飞鹤集罐活动报表
 * @summary 查询单个用户剩余可用罐数
 * @request POST:/firmus/collect/bottle/data/queryUserPot
 */
export const collectBottleDataQueryUserPot = (query: FirmusCollectBottleQuery): Promise<number> => {
  return httpRequest({
    url: '/firmus/collect/bottle/data/queryUserPot',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员中心访问成交数据
 * @request POST:/firmus/member/data/getMemberAccessGmvList
 */
export const memberDataGetMemberAccessGmvList = (
  firmusMemberGmvRequest: FirmusMemberGmvRequest,
): Promise<FirmusNewMemberAccessGmvResponse[]> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberAccessGmvList',
    method: 'post',
    data: firmusMemberGmvRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 预转段信息
 * @request POST:/firmus/member/data/getMemberBabyInfoList
 */
export const memberDataGetMemberBabyInfoList = (
  firmusMemberBabyInfoRequest: FirmusMemberBabyInfoRequest,
): Promise<IPageFirmusMemberBabyInfoResponse> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberBabyInfoList',
    method: 'post',
    data: firmusMemberBabyInfoRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 星币数据
 * @request POST:/firmus/member/data/getMemberCoinsList
 */
export const memberDataGetMemberCoinsList = (
  firmusMemberCoinsRequest: FirmusMemberCoinsRequest,
): Promise<FirmusMemberCoinsAllResponse> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberCoinsList',
    method: 'post',
    data: firmusMemberCoinsRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员情况总览
 * @request POST:/firmus/member/data/getMemberDataList
 */
export const memberDataGetMemberDataList = (
  firmusMemberDataRequest: FirmusMemberDataRequest,
): Promise<IPageFirmusMemberDataResponse> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberDataList',
    method: 'post',
    data: firmusMemberDataRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员当日数据一览
 * @request POST:/firmus/member/data/getMemberDataNow
 */
export const memberDataGetMemberDataNow = (): Promise<FirmusMemberDataResponse> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberDataNow',
    method: 'post',
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员前一日数据一览
 * @request POST:/firmus/member/data/getMemberDataYesterday
 */
export const memberDataGetMemberDataYesterday = (): Promise<FirmusMemberDataResponse> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberDataYesterday',
    method: 'post',
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员成交数据
 * @request POST:/firmus/member/data/getMemberGmvList
 */
export const memberDataGetMemberGmvList = (
  firmusMemberGmvRequest: FirmusMemberGmvRequest,
): Promise<FirmusNewMemberGmvResponse[]> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberGmvList',
    method: 'post',
    data: firmusMemberGmvRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 卓睿/非卓睿升降级数据
 * @request POST:/firmus/member/data/getMemberGradeList
 */
export const memberDataGetMemberGradeList = (
  firmusMemberUpgradeRequest: FirmusMemberUpgradeRequest,
): Promise<FirmusMemberGradeResponse> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberGradeList',
    method: 'post',
    data: firmusMemberUpgradeRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员等级成交本期数据——大数据
 * @request POST:/firmus/member/data/getMemberLevelGmvForBigData
 */
export const memberDataGetMemberLevelGmvForBigData = (
  firmusMemberLevelGmvDetailRequest: FirmusMemberLevelGmvForBigDataRequest,
): Promise<FirmusMemberLevelGmvForBigDataBizResponse> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberLevelGmvForBigData',
    method: 'post',
    data: firmusMemberLevelGmvDetailRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员等级成交数据
 * @request POST:/firmus/member/data/getMemberLevelGmvList
 */
export const memberDataGetMemberLevelGmvList = (
  firmusMemberLevelGmvRequest: FirmusMemberLevelGmvRequest,
): Promise<IPageFirmusMemberLevelGmvResponse> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberLevelGmvList',
    method: 'post',
    data: firmusMemberLevelGmvRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员等级成交本期数据一览
 * @request POST:/firmus/member/data/getMemberLevelGmvNowDetail
 */
export const memberDataGetMemberLevelGmvNowDetail = (
  firmusMemberLevelGmvDetailRequest: FirmusMemberLevelGmvDetailRequest,
): Promise<FirmusMemberLevelGmvDetailBizResponse> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberLevelGmvNowDetail',
    method: 'post',
    data: firmusMemberLevelGmvDetailRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 查询会员中心埋点数据
 * @request POST:/firmus/member/data/getMemberPointsList
 */
export const memberDataGetMemberPointsList = (
  firmusMemberPointsRequest: FirmusMemberPointsRequest,
): Promise<FirmusMemberPointsResponse[]> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberPointsList',
    method: 'post',
    data: firmusMemberPointsRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 查询会员中心领取数据
 * @request POST:/firmus/member/data/getMemberReceiveList
 */
export const memberDataGetMemberReceiveList = (
  firmusMemberReceiveRequest: FirmusMemberReceiveRequest,
): Promise<FirmusMemberReceiveBizResponse> => {
  return httpRequest({
    url: '/firmus/member/data/getMemberReceiveList',
    method: 'post',
    data: firmusMemberReceiveRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员中心访问成交数据导出
 * @request POST:/firmus/member/data/memberAccessGmvList/export
 */
export const memberDataMemberAccessGmvListExport = (firmusMemberGmvRequest: FirmusMemberGmvRequest): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberAccessGmvList/export',
    method: 'post',
    data: firmusMemberGmvRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 预转段信息导出
 * @request POST:/firmus/member/data/memberBabyInfoList/export
 */
export const memberDataMemberBabyInfoListExport = (
  firmusMemberBabyInfoRequest: FirmusMemberBabyInfoRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberBabyInfoList/export',
    method: 'post',
    data: firmusMemberBabyInfoRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 星币数据导出
 * @request POST:/firmus/member/data/memberCoinsList/export
 */
export const memberDataMemberCoinsListExport = (firmusMemberCoinsRequest: FirmusMemberCoinsRequest): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberCoinsList/export',
    method: 'post',
    data: firmusMemberCoinsRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员情况总览导出
 * @request POST:/firmus/member/data/memberDataList/export
 */
export const memberDataMemberDataListExport = (firmusMemberDataRequest: FirmusMemberDataRequest): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberDataList/export',
    method: 'post',
    data: firmusMemberDataRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员当日情况总览导出
 * @request POST:/firmus/member/data/memberDataNowList/export
 */
export const memberDataMemberDataNowListExport = (): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberDataNowList/export',
    method: 'post',
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员成交数据导出
 * @request POST:/firmus/member/data/memberGmvList/export
 */
export const memberDataMemberGmvListExport = (firmusMemberGmvRequest: FirmusMemberGmvRequest): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberGmvList/export',
    method: 'post',
    data: firmusMemberGmvRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 卓睿/非卓睿升降级数据导出
 * @request POST:/firmus/member/data/memberGradeList/export
 */
export const memberDataMemberGradeListExport = (
  firmusMemberUpgradeRequest: FirmusMemberUpgradeRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberGradeList/export',
    method: 'post',
    data: firmusMemberUpgradeRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员等级成交数据导出
 * @request POST:/firmus/member/data/memberLevelGmvList/export
 */
export const memberDataMemberLevelGmvListExport = (
  firmusMemberLevelGmvDetailRequest: FirmusMemberLevelGmvDetailRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberLevelGmvList/export',
    method: 'post',
    data: firmusMemberLevelGmvDetailRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 会员等级成交数据导出——大数据
 * @request POST:/firmus/member/data/memberLevelGmvListForBigData/export
 */
export const memberDataMemberLevelGmvListForBigDataExport = (
  firmusMemberLevelGmvRequest: FirmusMemberLevelGmvForBigDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberLevelGmvListForBigData/export',
    method: 'post',
    data: firmusMemberLevelGmvRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 查询会员中心埋点数据
 * @request POST:/firmus/member/data/memberPointsList/export
 */
export const memberDataMemberPointsListExport = (
  firmusMemberPointsRequest: FirmusMemberPointsRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberPointsList/export',
    method: 'post',
    data: firmusMemberPointsRequest,
  });
};

/**
 * @tags 飞鹤会员中心数据看板
 * @summary 查询会员中心领取数据
 * @request POST:/firmus/member/data/memberReceiveList/export
 */
export const memberDataMemberReceiveListExport = (
  firmusMemberReceiveRequest: FirmusMemberReceiveRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/data/memberReceiveList/export',
    method: 'post',
    data: firmusMemberReceiveRequest,
  });
};

/**
 * @tags 飞鹤会员中心装修
 * @summary 查询装修数据
 * @request POST:/firmus/member/getRenovation
 */
export const memberGetRenovation = (): Promise<ActivityMcpd1000003568Response> => {
  return httpRequest({
    url: '/firmus/member/getRenovation',
    method: 'post',
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员中心访问成交数据
 * @request POST:/firmus/member/pop/data/getMemberAccessGmvList
 */
export const memberPopDataGetMemberAccessGmvList = (
  firmusMemberGmvRequest: FirmusMemberGmvRequest,
): Promise<FirmusNewMemberAccessGmvResponse[]> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberAccessGmvList',
    method: 'post',
    data: firmusMemberGmvRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 预转段信息
 * @request POST:/firmus/member/pop/data/getMemberBabyInfoList
 */
export const memberPopDataGetMemberBabyInfoList = (
  firmusMemberBabyInfoRequest: FirmusMemberBabyInfoRequest,
): Promise<IPageFirmusMemberBabyInfoResponse> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberBabyInfoList',
    method: 'post',
    data: firmusMemberBabyInfoRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 星币数据
 * @request POST:/firmus/member/pop/data/getMemberCoinsList
 */
export const memberPopDataGetMemberCoinsList = (
  firmusMemberCoinsRequest: FirmusMemberCoinsRequest,
): Promise<FirmusMemberCoinsAllResponse> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberCoinsList',
    method: 'post',
    data: firmusMemberCoinsRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员情况总览
 * @request POST:/firmus/member/pop/data/getMemberDataList
 */
export const memberPopDataGetMemberDataList = (
  firmusMemberDataRequest: FirmusMemberDataRequest,
): Promise<IPageFirmusMemberDataResponse> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberDataList',
    method: 'post',
    data: firmusMemberDataRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员当日数据一览
 * @request POST:/firmus/member/pop/data/getMemberDataNow
 */
export const memberPopDataGetMemberDataNow = (): Promise<FirmusMemberDataResponse> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberDataNow',
    method: 'post',
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员前一日数据一览
 * @request POST:/firmus/member/pop/data/getMemberDataYesterday
 */
export const memberPopDataGetMemberDataYesterday = (): Promise<FirmusMemberDataResponse> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberDataYesterday',
    method: 'post',
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员成交数据
 * @request POST:/firmus/member/pop/data/getMemberGmvList
 */
export const memberPopDataGetMemberGmvList = (
  firmusMemberGmvRequest: FirmusMemberGmvRequest,
): Promise<FirmusNewMemberGmvResponse[]> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberGmvList',
    method: 'post',
    data: firmusMemberGmvRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 卓睿/非卓睿升降级数据
 * @request POST:/firmus/member/pop/data/getMemberGradeList
 */
export const memberPopDataGetMemberGradeList = (
  firmusMemberUpgradeRequest: FirmusMemberUpgradeRequest,
): Promise<FirmusMemberGradeResponse> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberGradeList',
    method: 'post',
    data: firmusMemberUpgradeRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员等级成交本期数据——大数据
 * @request POST:/firmus/member/pop/data/getMemberLevelGmvForBigData
 */
export const memberPopDataGetMemberLevelGmvForBigData = (
  firmusMemberLevelGmvDetailRequest: FirmusMemberLevelGmvForBigDataRequest,
): Promise<FirmusMemberLevelGmvForBigDataBizResponse> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberLevelGmvForBigData',
    method: 'post',
    data: firmusMemberLevelGmvDetailRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员等级成交数据
 * @request POST:/firmus/member/pop/data/getMemberLevelGmvList
 */
export const memberPopDataGetMemberLevelGmvList = (
  firmusMemberLevelGmvRequest: FirmusMemberLevelGmvRequest,
): Promise<IPageFirmusMemberLevelGmvResponse> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberLevelGmvList',
    method: 'post',
    data: firmusMemberLevelGmvRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员等级成交本期数据一览
 * @request POST:/firmus/member/pop/data/getMemberLevelGmvNowDetail
 */
export const memberPopDataGetMemberLevelGmvNowDetail = (
  firmusMemberLevelGmvDetailRequest: FirmusMemberLevelGmvDetailRequest,
): Promise<FirmusMemberLevelGmvDetailBizResponse> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberLevelGmvNowDetail',
    method: 'post',
    data: firmusMemberLevelGmvDetailRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 查询会员中心埋点数据
 * @request POST:/firmus/member/pop/data/getMemberPointsList
 */
export const memberPopDataGetMemberPointsList = (
  firmusMemberPointsRequest: FirmusMemberPointsRequest,
): Promise<FirmusMemberPointsResponse[]> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberPointsList',
    method: 'post',
    data: firmusMemberPointsRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 查询会员中心领取数据
 * @request POST:/firmus/member/pop/data/getMemberReceiveList
 */
export const memberPopDataGetMemberReceiveList = (
  firmusMemberReceiveRequest: FirmusMemberReceiveRequest,
): Promise<FirmusMemberReceiveBizResponse> => {
  return httpRequest({
    url: '/firmus/member/pop/data/getMemberReceiveList',
    method: 'post',
    data: firmusMemberReceiveRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员中心访问成交数据导出
 * @request POST:/firmus/member/pop/data/memberAccessGmvList/export
 */
export const memberPopDataMemberAccessGmvListExport = (
  firmusMemberGmvRequest: FirmusMemberGmvRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberAccessGmvList/export',
    method: 'post',
    data: firmusMemberGmvRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 预转段信息导出
 * @request POST:/firmus/member/pop/data/memberBabyInfoList/export
 */
export const memberPopDataMemberBabyInfoListExport = (
  firmusMemberBabyInfoRequest: FirmusMemberBabyInfoRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberBabyInfoList/export',
    method: 'post',
    data: firmusMemberBabyInfoRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 星币数据导出
 * @request POST:/firmus/member/pop/data/memberCoinsList/export
 */
export const memberPopDataMemberCoinsListExport = (
  firmusMemberCoinsRequest: FirmusMemberCoinsRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberCoinsList/export',
    method: 'post',
    data: firmusMemberCoinsRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员情况总览导出
 * @request POST:/firmus/member/pop/data/memberDataList/export
 */
export const memberPopDataMemberDataListExport = (firmusMemberDataRequest: FirmusMemberDataRequest): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberDataList/export',
    method: 'post',
    data: firmusMemberDataRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员当日情况总览导出
 * @request POST:/firmus/member/pop/data/memberDataNowList/export
 */
export const memberPopDataMemberDataNowListExport = (): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberDataNowList/export',
    method: 'post',
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员成交数据导出
 * @request POST:/firmus/member/pop/data/memberGmvList/export
 */
export const memberPopDataMemberGmvListExport = (firmusMemberGmvRequest: FirmusMemberGmvRequest): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberGmvList/export',
    method: 'post',
    data: firmusMemberGmvRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 卓睿/非卓睿升降级数据导出
 * @request POST:/firmus/member/pop/data/memberGradeList/export
 */
export const memberPopDataMemberGradeListExport = (
  firmusMemberUpgradeRequest: FirmusMemberUpgradeRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberGradeList/export',
    method: 'post',
    data: firmusMemberUpgradeRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员等级成交数据导出
 * @request POST:/firmus/member/pop/data/memberLevelGmvList/export
 */
export const memberPopDataMemberLevelGmvListExport = (
  firmusMemberLevelGmvDetailRequest: FirmusMemberLevelGmvDetailRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberLevelGmvList/export',
    method: 'post',
    data: firmusMemberLevelGmvDetailRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 会员等级成交数据导出——大数据
 * @request POST:/firmus/member/pop/data/memberLevelGmvListForBigData/export
 */
export const memberPopDataMemberLevelGmvListForBigDataExport = (
  firmusMemberLevelGmvRequest: FirmusMemberLevelGmvForBigDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberLevelGmvListForBigData/export',
    method: 'post',
    data: firmusMemberLevelGmvRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 查询会员中心埋点数据
 * @request POST:/firmus/member/pop/data/memberPointsList/export
 */
export const memberPopDataMemberPointsListExport = (
  firmusMemberPointsRequest: FirmusMemberPointsRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberPointsList/export',
    method: 'post',
    data: firmusMemberPointsRequest,
  });
};

/**
 * @tags 飞鹤POP会员中心数据看板
 * @summary 查询会员中心领取数据
 * @request POST:/firmus/member/pop/data/memberReceiveList/export
 */
export const memberPopDataMemberReceiveListExport = (
  firmusMemberReceiveRequest: FirmusMemberReceiveRequest,
): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/pop/data/memberReceiveList/export',
    method: 'post',
    data: firmusMemberReceiveRequest,
  });
};

/**
 * @tags 飞鹤会员中心装修
 * @summary 修改装修数据
 * @request POST:/firmus/member/updateRenovation
 */
export const memberUpdateRenovation = (req: ActivityMcpd1000003568SubmitRequest): Promise<void> => {
  return httpRequest({
    url: '/firmus/member/updateRenovation',
    method: 'post',
    data: req,
  });
};

/**
 * @tags 飞鹤pop流转数据看板
 * @summary 会员当日数据一览
 * @request POST:/firmus/pop/turn/export
 */
export const popTurnExport = (query: ExportTurnDataUsingPostParams): Promise<void> => {
  return httpRequest({
    url: '/firmus/pop/turn/export',
    method: 'post',
    params: query,
  });
};

/**
 * @tags 飞鹤pop流转数据看板
 * @summary 会员当日数据一览
 * @request POST:/firmus/pop/turn/getTurnData
 */
export const popTurnGetTurnData = (query: GetTurnDataUsingPostParams): Promise<FirmusPopTurnDataResponse> => {
  return httpRequest({
    url: '/firmus/pop/turn/getTurnData',
    method: 'post',
    params: query,
  });
};
