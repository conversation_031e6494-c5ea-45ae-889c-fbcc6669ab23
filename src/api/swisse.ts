import {
  BasePageRequest,
  EnterBatchDeleteUsingPostParams,
  EnterBatchImportRepeatInfoUsingPostParams,
  EnterCheckImportUsingPostParams,
  EnterImportUsingPostParams,
  EnterViewRepeatInfoUsingPostParams,
  ExportSwisseDeliveryEnterTemplateRequest,
  ExportUsingPost7Params,
  GetShippingMsgByInfoIdUsingPostParams,
  ImportKeyDetailUsingPostParams,
  IPageBdSwisseDeliveryChannel,
  IPageBdSwisseDeliveryFileLog,
  IPageBdSwisseDeliveryInfoHistory,
  IPageBdSwisseDeliveryKeyDetail,
  IPageBdSwisseDeliveryKeyPrize,
  IPageBdSwisseDeliveryPrize,
  IPageSwisseDeliveryEnterInfoTemplate,
  IPageSwisseDeliveryGetQueryInfoPageResponse,
  QueryDeleteInfoUsingPostParams,
  QueryGetInfoDetailUsingPostParams,
  QueryTopInfoUsingPostParams,
  SkuResponseResp,
  SwisseDeliveryChannelCreateOrUpdateRequest,
  SwisseDeliveryCreateKeyPrizeRequest,
  SwisseDeliveryEnterCheckImportResponse,
  SwisseDeliveryEnterGetImportInfoPageRequest,
  SwisseDeliveryGetChannelDataRequest,
  SwisseDeliveryGetInfoHistoryPageRequest,
  SwisseDeliveryGetKeyDataRequest,
  SwisseDeliveryGetKeyPrizeDataRequest,
  SwisseDeliveryGetMagerPageRequest,
  SwisseDeliveryGetPrizeDataRequest,
  SwisseDeliveryGetQueryInfoPageRequest,
  SwisseDeliveryGetQueryInfoPageResponse,
  SwisseDeliveryGetShippingMsgListResponse,
  SwisseDeliveryInfoEnterCreateInfoRequest,
  SwisseDeliveryInfoQueryBatchUpdatePrizeRequest,
  SwisseDeliveryInfoQueryBatchUpdateShippingStatusRequest,
  SwisseDeliveryInfoUpdateRequest,
  SwisseDeliveryKeyUpdateRequest,
  SwisseDeliveryPrizeCreateRequest,
  SwisseDeliveryPrizeUpdateRequest,
  SwisseDeliveryQueryGetInfoDetailResponse,
  SwisseDeliveryUpdateKeyPrizeRequest,
  SwisseLiveActivityInfoResponse,
  SwisseLiveCreateOrUpdateRequest,
  SwisseLiveCreateOrUpdateResponse,
  SwisseLiveDataAllReportResponse,
  SwisseLiveDataReportResponse,
  SwisseLiveDataRequest,
  SwisseLiveReportResponse,
  SwisseLiveUpdateSkuRequest,
  SwisseLsCombinedDataQuery,
  SwisseLsCombinedReportVO,
  UpDownChannelUsingPostParams,
  UpDownKeyUsingPostParams,
  UpDownPrizeUsingPostParams,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 斯维诗LS三合一数据看板
 * @summary 导出活动渠道数据报表
 * @request POST:/swisse/combined/channel/export
 */
export const combinedChannelExport = (query: SwisseLsCombinedDataQuery): Promise<void> => {
  return httpRequest({
    url: '/swisse/combined/channel/export',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 斯维诗LS三合一数据看板
 * @summary 分页查询活动渠道数据报表
 * @request POST:/swisse/combined/channel/query
 */
export const combinedChannelQuery = (query: SwisseLsCombinedDataQuery): Promise<SwisseLsCombinedReportVO> => {
  return httpRequest({
    url: '/swisse/combined/channel/query',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 斯维诗LS三合一数据看板
 * @summary 导出活动数据报表
 * @request POST:/swisse/combined/export
 */
export const combinedExport = (query: SwisseLsCombinedDataQuery): Promise<void> => {
  return httpRequest({
    url: '/swisse/combined/export',
    method: 'post',
    data: query,
  });
};

/**
 * @tags 斯维诗LS三合一数据看板
 * @summary 分页查询活动数据报表
 * @request POST:/swisse/combined/query
 */
export const combinedQuery = (query: SwisseLsCombinedDataQuery): Promise<SwisseLsCombinedReportVO> => {
  return httpRequest({
    url: '/swisse/combined/query',
    method: 'post',
    data: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/导入记录
 * @summary 获取文件上传日志
 * @request POST:/swisse/delivery/import/getFileLogPage
 */
export const deliveryImportGetFileLogPage = (request: BasePageRequest): Promise<IPageBdSwisseDeliveryFileLog> => {
  return httpRequest({
    url: '/swisse/delivery/import/getFileLogPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 渠道信息录入/批量删除
 * @request POST:/swisse/delivery/info/enter/batchDelete
 */
export const deliveryInfoEnterBatchDelete = (query: EnterBatchDeleteUsingPostParams): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/enter/batchDelete',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 渠道信息录入/选择导入重复数据
 * @request POST:/swisse/delivery/info/enter/batchImportRepeatInfo
 */
export const deliveryInfoEnterBatchImportRepeatInfo = (
  query: EnterBatchImportRepeatInfoUsingPostParams,
): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/enter/batchImportRepeatInfo',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 渠道信息录入/获取导入信息确认
 * @request POST:/swisse/delivery/info/enter/checkImport
 */
export const deliveryInfoEnterCheckImport = (
  query: EnterCheckImportUsingPostParams,
): Promise<SwisseDeliveryEnterCheckImportResponse> => {
  return httpRequest({
    url: '/swisse/delivery/info/enter/checkImport',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 渠道信息录入/新建录入信息
 * @request POST:/swisse/delivery/info/enter/createInfo
 */
export const deliveryInfoEnterCreateInfo = (request: SwisseDeliveryInfoEnterCreateInfoRequest): Promise<string> => {
  return httpRequest({
    url: '/swisse/delivery/info/enter/createInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 渠道信息录入/下载模板
 * @request POST:/swisse/delivery/info/enter/export
 */
export const deliveryInfoEnterExport = (request: ExportSwisseDeliveryEnterTemplateRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/enter/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 渠道信息录入/获取导入信息
 * @request POST:/swisse/delivery/info/enter/getImportInfo
 */
export const deliveryInfoEnterGetImportInfo = (
  request: SwisseDeliveryEnterGetImportInfoPageRequest,
): Promise<IPageSwisseDeliveryEnterInfoTemplate> => {
  return httpRequest({
    url: '/swisse/delivery/info/enter/getImportInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 渠道信息录入/导入信息
 * @request POST:/swisse/delivery/info/enter/import
 */
export const deliveryInfoEnterImport = (query: EnterImportUsingPostParams): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/enter/import',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 渠道信息录入/导入信息确认
 * @request POST:/swisse/delivery/info/enter/importConfirm
 */
export const deliveryInfoEnterImportConfirm = (file: any): Promise<string> => {
  return httpRequest({
    url: '/swisse/delivery/info/enter/importConfirm',
    method: 'post',
    data: file,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 渠道信息录入/查看历史重复信息
 * @request POST:/swisse/delivery/info/enter/viewRepeatInfo
 */
export const deliveryInfoEnterViewRepeatInfo = (
  query: EnterViewRepeatInfoUsingPostParams,
): Promise<SwisseDeliveryGetQueryInfoPageResponse[]> => {
  return httpRequest({
    url: '/swisse/delivery/info/enter/viewRepeatInfo',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 渠道信息录入/异常信息批量更改奖品
 * @request POST:/swisse/delivery/info/errorInfoBatchUpdatePrize
 */
export const deliveryInfoErrorInfoBatchUpdatePrize = (
  request: SwisseDeliveryInfoQueryBatchUpdatePrizeRequest,
): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/errorInfoBatchUpdatePrize',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传（历史）
 * @summary 获取奖品数据
 * @request POST:/swisse/delivery/info/getInfoHistPage
 */
export const deliveryInfoGetInfoHistPage = (
  request: SwisseDeliveryGetInfoHistoryPageRequest,
): Promise<IPageBdSwisseDeliveryInfoHistory> => {
  return httpRequest({
    url: '/swisse/delivery/info/getInfoHistPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 信息查询/批量更改奖品
 * @request POST:/swisse/delivery/info/query/batchUpdatePrize
 */
export const deliveryInfoQueryBatchUpdatePrize = (
  request: SwisseDeliveryInfoQueryBatchUpdatePrizeRequest,
): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/query/batchUpdatePrize',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 信息查询/批量更改发货状态
 * @request POST:/swisse/delivery/info/query/batchUpdateShippingStatus
 */
export const deliveryInfoQueryBatchUpdateShippingStatus = (
  request: SwisseDeliveryInfoQueryBatchUpdateShippingStatusRequest,
): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/query/batchUpdateShippingStatus',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 信息查询/删除信息
 * @request POST:/swisse/delivery/info/query/deleteInfo
 */
export const deliveryInfoQueryDeleteInfo = (query: QueryDeleteInfoUsingPostParams): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/query/deleteInfo',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 信息查询/是否有新录入异常信息
 * @request POST:/swisse/delivery/info/query/exceptionStatus
 */
export const deliveryInfoQueryExceptionStatus = (): Promise<boolean> => {
  return httpRequest({
    url: '/swisse/delivery/info/query/exceptionStatus',
    method: 'post',
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 信息查询/查询详情
 * @request POST:/swisse/delivery/info/query/getInfoDetail
 */
export const deliveryInfoQueryGetInfoDetail = (
  query: QueryGetInfoDetailUsingPostParams,
): Promise<SwisseDeliveryQueryGetInfoDetailResponse> => {
  return httpRequest({
    url: '/swisse/delivery/info/query/getInfoDetail',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 信息查询/获取奖品数据
 * @request POST:/swisse/delivery/info/query/getInfoPage
 */
export const deliveryInfoQueryGetInfoPage = (
  request: SwisseDeliveryGetQueryInfoPageRequest,
): Promise<IPageSwisseDeliveryGetQueryInfoPageResponse> => {
  return httpRequest({
    url: '/swisse/delivery/info/query/getInfoPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 信息查询/导出奖品数据
 * @request POST:/swisse/delivery/info/query/getInfoPage/export
 */
export const deliveryInfoQueryGetInfoPageExport = (request: SwisseDeliveryGetQueryInfoPageRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/query/getInfoPage/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 信息查询/获取卡密明细
 * @request POST:/swisse/delivery/info/query/getKeyDetail
 */
export const deliveryInfoQueryGetKeyDetail = (
  query: GetShippingMsgByInfoIdUsingPostParams,
): Promise<SwisseDeliveryGetShippingMsgListResponse[]> => {
  return httpRequest({
    url: '/swisse/delivery/info/query/getKeyDetail',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 信息查询/置顶奖品信息
 * @request POST:/swisse/delivery/info/query/topInfo
 */
export const deliveryInfoQueryTopInfo = (query: QueryTopInfoUsingPostParams): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/query/topInfo',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/信息上传
 * @summary 信息查询/更新奖品信息
 * @request POST:/swisse/delivery/info/query/updateInfo
 */
export const deliveryInfoQueryUpdateInfo = (request: SwisseDeliveryInfoUpdateRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/info/query/updateInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/发货管理
 * @summary 发货管理/导出奖品数据
 * @request POST:/swisse/delivery/manage/getInfo/export
 */
export const deliveryManageGetInfoExport = (request: SwisseDeliveryGetMagerPageRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/manage/getInfo/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/发货管理
 * @summary 发货管理/获取奖品数据
 * @request POST:/swisse/delivery/manage/getInfoPage
 */
export const deliveryManageGetInfoPage = (
  request: SwisseDeliveryGetMagerPageRequest,
): Promise<IPageSwisseDeliveryGetQueryInfoPageResponse> => {
  return httpRequest({
    url: '/swisse/delivery/manage/getInfoPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/发货管理
 * @summary 发货管理/导出奖品为等待发货
 * @request POST:/swisse/delivery/manage/getInfoWithUpdate/export
 */
export const deliveryManageGetInfoWithUpdateExport = (request: SwisseDeliveryGetMagerPageRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/manage/getInfoWithUpdate/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/发货管理
 * @summary 发货管理/一键导出所有等待发货
 * @request POST:/swisse/delivery/manage/getWaitInfo/export
 */
export const deliveryManageGetWaitInfoExport = (request: SwisseDeliveryGetMagerPageRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/manage/getWaitInfo/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/发货管理
 * @summary 发货管理/导入物流单号
 * @request POST:/swisse/delivery/manage/importShippingInfo
 */
export const deliveryManageImportShippingInfo = (file: any): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/manage/importShippingInfo',
    method: 'post',
    data: file,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 创建渠道
 * @request POST:/swisse/delivery/prize/createChannel
 */
export const deliveryPrizeCreateChannel = (request: SwisseDeliveryChannelCreateOrUpdateRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/createChannel',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 创建卡密计划
 * @request POST:/swisse/delivery/prize/createKeyPrize
 */
export const deliveryPrizeCreateKeyPrize = (request: SwisseDeliveryCreateKeyPrizeRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/createKeyPrize',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 创建奖品
 * @request POST:/swisse/delivery/prize/createPrize
 */
export const deliveryPrizeCreatePrize = (request: SwisseDeliveryPrizeCreateRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/createPrize',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 下载模板
 * @request POST:/swisse/delivery/prize/export
 */
export const deliveryPrizeExport = (query: ExportUsingPost7Params): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/export',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 获取渠道数据
 * @request POST:/swisse/delivery/prize/getChannelData
 */
export const deliveryPrizeGetChannelData = (
  request: SwisseDeliveryGetChannelDataRequest,
): Promise<IPageBdSwisseDeliveryChannel> => {
  return httpRequest({
    url: '/swisse/delivery/prize/getChannelData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 获取卡密明细数据
 * @request POST:/swisse/delivery/prize/getKeyData
 */
export const deliveryPrizeGetKeyData = (
  request: SwisseDeliveryGetKeyDataRequest,
): Promise<IPageBdSwisseDeliveryKeyDetail> => {
  return httpRequest({
    url: '/swisse/delivery/prize/getKeyData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 获取卡密计划
 * @request POST:/swisse/delivery/prize/getKeyPrizeData
 */
export const deliveryPrizeGetKeyPrizeData = (
  request: SwisseDeliveryGetKeyPrizeDataRequest,
): Promise<IPageBdSwisseDeliveryKeyPrize> => {
  return httpRequest({
    url: '/swisse/delivery/prize/getKeyPrizeData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 获取奖品数据
 * @request POST:/swisse/delivery/prize/getPrizeData
 */
export const deliveryPrizeGetPrizeData = (
  request: SwisseDeliveryGetPrizeDataRequest,
): Promise<IPageBdSwisseDeliveryPrize> => {
  return httpRequest({
    url: '/swisse/delivery/prize/getPrizeData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 导出奖品
 * @request POST:/swisse/delivery/prize/getPrizeData/export
 */
export const deliveryPrizeGetPrizeDataExport = (request: SwisseDeliveryGetPrizeDataRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/getPrizeData/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 导入奖品
 * @request POST:/swisse/delivery/prize/importKeyDetail
 */
export const deliveryPrizeImportKeyDetail = (query: ImportKeyDetailUsingPostParams, file: any): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/importKeyDetail',
    method: 'post',
    params: query,
    data: file,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 上下架渠道
 * @request POST:/swisse/delivery/prize/upDownChannel
 */
export const deliveryPrizeUpDownChannel = (query: UpDownChannelUsingPostParams): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/upDownChannel',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 上下架卡密
 * @request POST:/swisse/delivery/prize/upDownKey
 */
export const deliveryPrizeUpDownKey = (query: UpDownKeyUsingPostParams): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/upDownKey',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 上下架奖品
 * @request POST:/swisse/delivery/prize/upDownPrize
 */
export const deliveryPrizeUpDownPrize = (query: UpDownPrizeUsingPostParams): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/upDownPrize',
    method: 'post',
    params: query,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 修改渠道
 * @request POST:/swisse/delivery/prize/updateChannel
 */
export const deliveryPrizeUpdateChannel = (request: SwisseDeliveryChannelCreateOrUpdateRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/updateChannel',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 编辑卡密明细数据
 * @request POST:/swisse/delivery/prize/updateKeyData
 */
export const deliveryPrizeUpdateKeyData = (request: SwisseDeliveryKeyUpdateRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/updateKeyData',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 更新卡密计划
 * @request POST:/swisse/delivery/prize/updateKeyPrize
 */
export const deliveryPrizeUpdateKeyPrize = (request: SwisseDeliveryUpdateKeyPrizeRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/updateKeyPrize',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse发货后台奖品管理/奖品管理
 * @summary 修改奖品
 * @request POST:/swisse/delivery/prize/updatePrize
 */
export const deliveryPrizeUpdatePrize = (request: SwisseDeliveryPrizeUpdateRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/delivery/prize/updatePrize',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse会员页面装修
 * @summary 获取swisse会员页面装修json
 * @request POST:/swisse/getPageJson
 */
export const getPageJson = (): Promise<object> => {
  return httpRequest({
    url: '/swisse/getPageJson',
    method: 'post',
  });
};

/**
 * @tags swisse会员页面装修
 * @summary 斯维诗会员中心曝光商品接口
 * @request POST:/swisse/getSkus
 */
export const getSkus = (params: Record<string, string>): Promise<SkuResponseResp[]> => {
  return httpRequest({
    url: '/swisse/getSkus',
    method: 'post',
    data: params,
  });
};

/**
 * @tags Swisse斯维诗半屏直播数据看板
 * @summary 数据一览
 * @request POST:/swisse/live/data/getMemberData
 */
export const liveDataGetMemberData = (): Promise<SwisseLiveDataAllReportResponse> => {
  return httpRequest({
    url: '/swisse/live/data/getMemberData',
    method: 'post',
  });
};

/**
 * @tags Swisse斯维诗半屏直播数据看板
 * @summary 会员当日数据一览
 * @request POST:/swisse/live/data/getMemberDataNow
 */
export const liveDataGetMemberDataNow = (request: SwisseLiveDataRequest): Promise<SwisseLiveDataReportResponse> => {
  return httpRequest({
    url: '/swisse/live/data/getMemberDataNow',
    method: 'post',
    data: request,
  });
};

/**
 * @tags Swisse斯维诗半屏直播数据看板
 * @summary Swisse斯维诗半屏直播数据导出
 * @request POST:/swisse/live/data/getMemberDataNow/export
 */
export const liveDataGetMemberDataNowExport = (request: SwisseLiveDataRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/live/data/getMemberDataNow/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags Swisse斯维诗半屏直播数据看板
 * @summary 订单数据一览
 * @request POST:/swisse/live/data/getOrder
 */
export const liveDataGetOrder = (): Promise<SwisseLiveReportResponse[]> => {
  return httpRequest({
    url: '/swisse/live/data/getOrder',
    method: 'post',
  });
};

/**
 * @tags Swisse斯维诗直播半屏
 * @summary 获取活动信息
 * @request POST:/swisse/live/getActivityInfo
 */
export const liveGetActivityInfo = (): Promise<SwisseLiveActivityInfoResponse> => {
  return httpRequest({
    url: '/swisse/live/getActivityInfo',
    method: 'post',
  });
};

/**
 * @tags Swisse斯维诗直播半屏
 * @summary 修改活动
 * @request POST:/swisse/live/updateActivity
 */
export const liveUpdateActivity = (
  request: SwisseLiveCreateOrUpdateRequest,
): Promise<SwisseLiveCreateOrUpdateResponse> => {
  return httpRequest({
    url: '/swisse/live/updateActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags Swisse斯维诗直播半屏
 * @summary 更新生效中子id的曝光商品
 * @request POST:/swisse/live/updateSku
 */
export const liveUpdateSku = (request: SwisseLiveUpdateSkuRequest): Promise<void> => {
  return httpRequest({
    url: '/swisse/live/updateSku',
    method: 'post',
    data: request,
  });
};

/**
 * @tags swisse会员页面装修
 * @summary 获取swisse会员页面数据
 * @request POST:/swisse/memberData/memberPageData/export
 */
export const memberDataMemberPageDataExport = (params: Record<string, string>): Promise<void> => {
  return httpRequest({
    url: '/swisse/memberData/memberPageData/export',
    method: 'post',
    data: params,
  });
};

/**
 * @tags swisse会员页面装修
 * @summary 获取swisse会员页面数据
 * @request POST:/swisse/memberData/memberPageData/getData
 */
export const memberDataMemberPageDataGetData = (params: Record<string, string>): Promise<object> => {
  return httpRequest({
    url: '/swisse/memberData/memberPageData/getData',
    method: 'post',
    data: params,
  });
};

/**
 * @tags swisse会员页面装修
 * @summary 获取swisse会员页面数据
 * @request POST:/swisse/memberData/memberPageData/getGroups
 */
export const memberDataMemberPageDataGetGroups = (params: Record<string, string>): Promise<object> => {
  return httpRequest({
    url: '/swisse/memberData/memberPageData/getGroups',
    method: 'post',
    data: params,
  });
};

/**
 * @tags swisse会员页面装修
 * @summary 更新swisse会员页面装修json——smartCard
 * @request POST:/swisse/savePageJson
 */
export const savePageJson = (params: object): Promise<boolean> => {
  return httpRequest({
    url: '/swisse/savePageJson',
    method: 'post',
    data: params,
  });
};

/**
 * @tags swisse会员页面装修
 * @summary 更新swisse会员页面装修json——smartCard
 * @request POST:/swisse/savePageJsonDraft
 */
export const savePageJsonDraft = (params: Record<string, string>): Promise<boolean> => {
  return httpRequest({
    url: '/swisse/savePageJsonDraft',
    method: 'post',
    data: params,
  });
};
