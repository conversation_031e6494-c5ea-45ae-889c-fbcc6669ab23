import {
  Activity99002CreateOrUpdateRequest,
  Activity99002CreateOrUpdateResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 集XXX抽奖
 * @summary 创建活动
 * @request POST:/99002/createActivity
 */
export const createActivity = (
  request: Activity99002CreateOrUpdateRequest,
): Promise<Activity99002CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99002/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 集XXX抽奖
 * @summary 查询活动信息
 * @request POST:/99002/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/99002/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 集XXX抽奖
 * @summary 修改活动
 * @request POST:/99002/updateActivity
 */
export const updateActivity = (
  request: Activity99002CreateOrUpdateRequest,
): Promise<Activity99002CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99002/updateActivity',
    method: 'post',
    data: request,
  });
};
