import {
  Activity90101ActivityDataRequest,
  Activity92006CreateOrUpdateRequest,
  Activity92006CreateOrUpdateResponse,
  Activity92006DataRequest,
  Activity92006SkuVo,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageActivity92006AddChanceResponse,
  IPageActivity92006DrawResponse,
  IPageActivity92006OrderChanceResponse,
  IPageBdActivity92006Data,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 新鲜购
 * @summary 创建活动
 * @request POST:/92006/createActivity
 */
export const createActivity = (
  request: Activity92006CreateOrUpdateRequest,
): Promise<Activity92006CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/92006/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 新鲜购
 * @summary 加购的机会记录
 * @request POST:/92006/data/addDrawLog
 */
export const dataAddDrawLog = (
  activity90101ActivityDataRequest: Activity90101ActivityDataRequest,
): Promise<IPageActivity92006AddChanceResponse> => {
  return httpRequest({
    url: '/92006/data/addDrawLog',
    method: 'post',
    data: activity90101ActivityDataRequest,
  });
};

/**
 * @tags 新鲜购
 * @summary 加购的机会记录导出
 * @request POST:/92006/data/addDrawLog/export
 */
export const dataAddDrawLogExport = (
  activity90101ActivityDataRequest: Activity90101ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/92006/data/addDrawLog/export',
    method: 'post',
    data: activity90101ActivityDataRequest,
  });
};

/**
 * @tags 新鲜购
 * @summary 每日数据
 * @request POST:/92006/data/dailyData
 */
export const dataDailyData = (
  activity92006DataRequest: Activity92006DataRequest,
): Promise<IPageBdActivity92006Data> => {
  return httpRequest({
    url: '/92006/data/dailyData',
    method: 'post',
    data: activity92006DataRequest,
  });
};

/**
 * @tags 新鲜购
 * @summary 每日数据导出
 * @request POST:/92006/data/dailyData/export
 */
export const dataDailyDataExport = (activity92006DataRequest: Activity92006DataRequest): Promise<void> => {
  return httpRequest({
    url: '/92006/data/dailyData/export',
    method: 'post',
    data: activity92006DataRequest,
  });
};

/**
 * @tags 新鲜购
 * @summary 下单的机会记录
 * @request POST:/92006/data/orderDrawLog
 */
export const dataOrderDrawLog = (
  activity90101ActivityDataRequest: Activity90101ActivityDataRequest,
): Promise<IPageActivity92006OrderChanceResponse> => {
  return httpRequest({
    url: '/92006/data/orderDrawLog',
    method: 'post',
    data: activity90101ActivityDataRequest,
  });
};

/**
 * @tags 新鲜购
 * @summary 下单的机会记录导出
 * @request POST:/92006/data/orderDrawLog/export
 */
export const dataOrderDrawLogExport = (
  activity90101ActivityDataRequest: Activity90101ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/92006/data/orderDrawLog/export',
    method: 'post',
    data: activity90101ActivityDataRequest,
  });
};

/**
 * @tags 新鲜购
 * @summary 加购的抽奖记录
 * @request POST:/92006/data/winningAddDrawLog
 */
export const dataWinningAddDrawLog = (
  activity90101ActivityDataRequest: Activity90101ActivityDataRequest,
): Promise<IPageActivity92006DrawResponse> => {
  return httpRequest({
    url: '/92006/data/winningAddDrawLog',
    method: 'post',
    data: activity90101ActivityDataRequest,
  });
};

/**
 * @tags 新鲜购
 * @summary 加购的抽奖记录导出
 * @request POST:/92006/data/winningAddDrawLog/export
 */
export const dataWinningAddDrawLogExport = (
  activity90101ActivityDataRequest: Activity90101ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/92006/data/winningAddDrawLog/export',
    method: 'post',
    data: activity90101ActivityDataRequest,
  });
};

/**
 * @tags 新鲜购
 * @summary 下单的抽奖记录
 * @request POST:/92006/data/winningOrderDrawLog
 */
export const dataWinningOrderDrawLog = (
  activity90101ActivityDataRequest: Activity90101ActivityDataRequest,
): Promise<IPageActivity92006DrawResponse> => {
  return httpRequest({
    url: '/92006/data/winningOrderDrawLog',
    method: 'post',
    data: activity90101ActivityDataRequest,
  });
};

/**
 * @tags 新鲜购
 * @summary 下单的抽奖记录导出
 * @request POST:/92006/data/winningOrderDrawLog/export
 */
export const dataWinningOrderDrawLogExport = (
  activity90101ActivityDataRequest: Activity90101ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/92006/data/winningOrderDrawLog/export',
    method: 'post',
    data: activity90101ActivityDataRequest,
  });
};

/**
 * @tags 新鲜购
 * @summary 查询活动信息
 * @request POST:/92006/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/92006/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 新鲜购
 * @summary 导入系列信息excel
 * @request POST:/92006/importSeriesExcel
 */
export const importSeriesExcel = (file: any): Promise<Activity92006SkuVo[]> => {
  return httpRequest({
    url: '/92006/importSeriesExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 新鲜购
 * @summary 下载模板
 * @request POST:/92006/seriesTemplate/export
 */
export const seriesTemplateExport = (): Promise<void> => {
  return httpRequest({
    url: '/92006/seriesTemplate/export',
    method: 'post',
  });
};

/**
 * @tags 新鲜购
 * @summary 修改活动
 * @request POST:/92006/updateActivity
 */
export const updateActivity = (
  request: Activity92006CreateOrUpdateRequest,
): Promise<Activity92006CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/92006/updateActivity',
    method: 'post',
    data: request,
  });
};
