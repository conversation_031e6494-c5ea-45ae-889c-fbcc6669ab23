import {
  Activity95002CreateOrUpdateRequest,
  Activity95002CreateOrUpdateResponse,
  Activity95002UserPrizeRecordPageRequest,
  IPageActivity95002UserPrizeRecordPageResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 满额有礼-手动领取
 * @summary 创建活动
 * @request POST:/95002/createActivity
 */
export const createActivity = (
  request: Activity95002CreateOrUpdateRequest,
): Promise<Activity95002CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/95002/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 消费满额有礼手动领取数据
 * @summary 中奖记录
 * @request POST:/95002/data/winningLog
 */
export const dataWinningLog = (
  request: Activity95002UserPrizeRecordPageRequest,
): Promise<IPageActivity95002UserPrizeRecordPageResponse> => {
  return httpRequest({
    url: '/95002/data/winningLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 消费满额有礼手动领取数据
 * @summary 中奖记录导出
 * @request POST:/95002/data/winningLog/export
 */
export const dataWinningLogExport = (request: Activity95002UserPrizeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/95002/data/winningLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 满额有礼-手动领取
 * @summary 修改活动
 * @request POST:/95002/updateActivity
 */
export const updateActivity = (
  request: Activity95002CreateOrUpdateRequest,
): Promise<Activity95002CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/95002/updateActivity',
    method: 'post',
    data: request,
  });
};
