import dayjs from 'dayjs';
import { APP_MODE, config } from 'ice';
import { getShopOrderTime } from '@/api/common';
import { event } from '@ice/stark-data';
import { appHistory } from '@ice/stark-app';

export const isDev = APP_MODE === 'dev' || APP_MODE === 'local';

const mask = (str, maskLength = 4) => {
  const maskStr = Array.from(Array(maskLength), () => '*').join('');
  if (str && str.length >= 2) {
    return `${str.substr(0, 1)}${maskStr}${str.substr(str.length - 1, 1)}`;
  }
  return str;
};

const prizeTypeUrlMap = {
  1: '/property/coupon',
  2: '/property/bean',
  3: '/property/product',
  6: '/property/redPacket',
  7: '/property/giftCardManage',
  8: '/property/JDECard',
  9: '/property/EPLUS',
  10: '/property/iqiyicard',
  12: '/property/yuanbao/benefitPlanManagement',
};
export const goToPropertyList = (prizeType) => {
  const hasActivityManage = window.location.href.includes('activityManage');
  const url = prizeTypeUrlMap[prizeType];
  if (hasActivityManage && url) {
    appHistory.push(url);
  }
};

// 判断商家促活需要展示的白名单，如果需要展示，直接弹出任务完成提示，不展示就不做操作
export const promotionComplete = async (taskId) => {
  // const shopId = JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') || '')?.shopId;
  // const res = await getCanvasPower({
  //   listKey: 'shop_active', // 商家促活
  //   listType: 1, // 1白名单 0黑名单
  // });
  // if (res.includes(Number(shopId))) {
  //   event?.emit('LZ_PROMOTION_COMPLETE_DIALOG', taskId);
  // }

  const type = JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') as string)?.versionNo;
  if ([5, 6].indexOf(type) > -1) {
    event?.emit('LZ_PROMOTION_COMPLETE_DIALOG', taskId);
  }
};

let shopOrderStartTime = '';
export const getShopOrderStartTime = async () => {
  try {
    if (!shopOrderStartTime) {
      const data = await getShopOrderTime();
      shopOrderStartTime = data;
    }
    if (+shopOrderStartTime <= 10000) {
      return {
        shopOrderStartTime: +shopOrderStartTime,
        longTermOrder: false,
      };
    }
    return {
      shopOrderStartTime: +shopOrderStartTime,
      longTermOrder: true,
    };
  } catch (error) {
    return {
      shopOrderStartTime: 180,
      longTermOrder: false,
    };
  }
};
const maskMobile = (str) => {
  if (str) {
    if (str.length <= 8) {
      return mask(str);
    } else {
      const maskStr = Array.from(Array(4), () => '*').join('');
      return `${str.substr(0, 4)}${maskStr}${str.substr(str.length - 4, 4)}`;
    }
  }
  return str;
};

const maskAddress = (str) => {
  if (str) {
    if (str.length <= 8) {
      return mask(str);
    } else {
      const maskStr = Array.from(Array(4), () => '*').join('');
      return `${str.substr(0, 4)}${maskStr}${str.substr(str.length - 4, 4)}`;
    }
  }
  return str;
};

/**
 * 从URL上获取指定参数
 * @param key [string]
 */
export const getParams = (key: string): string => {
  const result = new URLSearchParams(window.location.search);
  return result.get(key) as string;
};

/**
 * 校验大于0正整数
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 */
export const numRegularCheckInt = (rule: any, value: any, callback: any) => {
  if (value && !/(^[1-9]\d*$)/.test(value)) {
    callback('请输入正整数');
  } else {
    callback();
  }
};

export const deepCopy = (obj) => {
  return obj ? JSON.parse(JSON.stringify(obj)) : obj;
};

export default {
  copyText(text) {
    return new Promise<void>((resolve) => {
      const tag = document.createElement('input');
      tag.setAttribute('id', 'cp_hgz_input_new');
      tag.value = text;
      document.getElementsByTagName('body')[0].appendChild(tag);
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      document.getElementById('cp_hgz_input_new').select();
      document.execCommand('copy');
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      document.getElementById('cp_hgz_input_new').remove();
      resolve();
    });
  },
  maskAddress,
  maskMobile,
  mask,
};

/**
 * 编辑态禁用相关
 */
export const activityEditDisabled = (): boolean => {
  const type = getParams('type');
  const status = +getParams('status');
  return status === 2 && type === 'edit';
};

/**
 * 从url上获取活动信息
 * @response [activityId, activityType, templateCode]
 */
export const getActivityParams = (): number[] => {
  return location.pathname
    .split('/')
    .filter((e) => +e)
    .map((e) => +e);
};

/**
 * 生成jd app预览地址
 * @param path
 */
export const getJdAppPreviewUrl = (path: string): string => {
  if (isDev) {
    return `https://lzkj-isv.isvjcloud.com/test/cc/cjwx/m/demo/jssdk/jump/index.html?target=${encodeURIComponent(
      config.previewUrl + path,
    )}`;
  } else {
    return config.previewUrl + path;
  }
};

/**
 * 下载excel
 * @param data
 * @param data.file 文件
 * @param data.suffix 后缀
 * @param fileName 文件名
 * */
export const downloadExcel = (data: { file: Blob; suffix: string }, fileName?: string) => {
  console.log(data);
  const { file, suffix } = data;
  const url = window.URL.createObjectURL(new Blob([file]));
  const link: HTMLAnchorElement = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute(
    'download',
    fileName && fileName !== '' ? `${fileName}${suffix}` : `导出数据${dayjs().format('YYYY-MM-DD')}${suffix}`,
  );
  document.body.appendChild(link);
  link.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(link);
};

export const calcDateDiff = (rangeDate: any[]): string => {
  let duration = '';
  if (rangeDate && rangeDate.length === 2) {
    const diffDay: number = dayjs(rangeDate[1]).diff(rangeDate[0], 'day');
    const diffHour: number = dayjs(rangeDate[1]).diff(rangeDate[0], 'hour') - diffDay * 24;
    const diffDayStr = `${diffDay > 0 ? `${diffDay}天` : ''}`;
    const diffHourStr = `${diffHour > 0 ? `${diffHour}小时` : ''}`;
    duration = diffDayStr + diffHourStr;
  }
  return duration;
};

export const removeOlderInteractSessionStorage = () => {
  const keysToDelete = [
    'templateCode',
    'selectValueB',
    'oldImagePath',
    'customizedStatus',
    'templateId',
    'securityStatus',
    'breadName',
    'configId',
    'selectIndexB',
    'updataImageB',
    'customJsonData',
    'jdShareImg',
    '138615350',
    'shareTitle',
    'advertisementStatus',
    'pageNoB',
    'itemAcData',
    'activityStatus',
    'shareImg',
    'auctionData',
    'templateData',
    'acTime',
    'isV2Come',
  ];
  for (const key of keysToDelete) {
    try {
      sessionStorage.removeItem(key);
    } catch (e) {
      console.log('当前key不存在');
    }
  }
};

/**
 * 判断字符串是否是json格式
 * @param str 字符串
 */
export const isJSONFormat = (str: string) => {
  try {
    const obj = JSON.parse(str);
    return !!(typeof obj === 'object' && obj);
  } catch (e) {
    console.log(`error：${str}!!!${e}`);
    return false;
  }
};
/**
 * 判断自营店铺还是pop店铺
 */
export const isPopShop = () => {
  const { venderType } = JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') || '{}');
  return !venderType;
};

/**
 * 判断是否为空对象
 */
export const isEmptyObject = (obj: any) => {
  for (const key in obj) {
    // eslint-disable-next-line no-prototype-builtins
    if (obj.hasOwnProperty(key)) {
      return false; // 如果对象有任何属性，就不是空对象
    }
  }
  return true; // 如果没有任何属性，就是空对象
};
/**
 * 防抖
 */
export const debounce = (func, wait = 50) => {
  let timer: any = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    timer = setTimeout(() => func.apply(this, args), wait);
  };
};

/**
 * 验证活动门槛
 */
export const validateActivityThreshold = (formData, err, field) => {
  const errorMappings = {
    0: { delete: ['supportLevels', 'crowdBag'], setErrors: { supportLevels: '', crowdBag: '' } },
    1: { delete: ['crowdBag'], setErrors: { crowdBag: '' } },
    2: { delete: ['supportLevels'], setErrors: { supportLevels: '' } },
  };
  if (err) {
    const mapping = errorMappings[formData.threshold];
    if (mapping) {
      mapping.delete.forEach((prop) => delete err[prop]);
      if (isEmptyObject(err)) {
        err = null;
      }
      field.setErrors(mapping.setErrors);
    }
  }
  return err;
};

export const isDisableSetPrize = (prizeList: any, index: number) => {
  const prizeLength: number = prizeList.filter((e) => e.prizeName !== '谢谢参与').length;
  if (prizeLength === 7) {
    const noPrizeIndex = prizeList.findIndex((e) => e.prizeName === '谢谢参与');
    if (noPrizeIndex === index) {
      return true;
    }
  }
  return false;
};

/** 延迟，返回 Promise */
export function delay(delayTime: number) {
  return new Promise((rs) => {
    setTimeout(rs, delayTime);
  });
}
