import { Radio, Form, Button, NumberPicker, Input, Grid, Field, Message } from '@alifd/next';
import React, { useReducer } from 'react';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import { numRegularCheckInt } from '@/utils';
import { prizeFormLayout } from '@/components/ChoosePrize';

interface ComponentProps {
  [propName: string]: any;
}

const PropertyJdPoints = ({
  editValue,
  onChange,
  onCancel,
  hasLimit = true,
  hasProbability = true,
  hasOrderPrice = false,
  width,
  height,
  prizeNameLength,
  formData,
  sendTotalCountMin = 1,
  sendTotalCountMax = 999999999,
}: ComponentProps) => {
  const equityImg = '//img10.360buyimg.com/imgzone/jfs/t1/172629/8/10529/9649/60a4cb50Edc2ee40a/b53b1246de5f2089.png';
  const defaultValue = {
    prizeKey: null,
    prizeType: 4,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: equityImg,
  };
  const field = Field.useField();

  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  const submit = (values: any, errors: any): boolean | void => {
    if (hasLimit && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }
    // if (hasProbability && +prizeData.probability <= 0) {
    //   Message.error(`中奖概率必须大于0`);
    //   return false;
    // }
    if (hasProbability && +prizeData.probability + +formData.totalProbability >= 100) {
      Message.error(`中奖总概率不能超过100`);
      return false;
    }
    !errors && onChange(prizeData);
  };
  return (
    <div className={styles.PropertyJdPoints}>
      <Form field={field} {...prizeFormLayout}>
        <Form.Item label="单次发放量" required requiredMessage="请输入单次发放量" validator={numRegularCheckInt}>
          <NumberPicker
            className={styles.formNumberPicker}
            name="unitCount"
            type="inline"
            min={1}
            max={999999999}
            step={1}
            value={prizeData.unitCount}
            onChange={(unitCount: any) => {
              setData({ unitCount, prizeName: unitCount ? `${unitCount}积分` : '' });
              field.setErrors({ prizeName: '' });
            }}
          />
          积分
        </Form.Item>
        <Form.Item label="单份价值" required requiredMessage="请输入单份价值">
          <NumberPicker
            className={styles.formNumberPicker}
            onChange={(unitPrice: any) => setData({ unitPrice })}
            name="unitPrice"
            type="inline"
            precision={2}
            max={9999999}
            min={0.01}
            value={prizeData.unitPrice}
          />
          元
        </Form.Item>
        {hasOrderPrice && (
          <Form.Item label="获奖消费额度" required requiredMessage="请输入获奖消费额度">
            <NumberPicker
              className={styles.formNumberPicker}
              onChange={(orderPrice: any) => setData({ orderPrice })}
              name="orderPrice"
              type="inline"
              min={0}
              max={9999999}
              precision={2}
              value={prizeData.orderPrice}
            />
            元
          </Form.Item>
        )}
        <Form.Item label="发放份数" required requiredMessage="请输入发放份数" validator={numRegularCheckInt}>
          <NumberPicker
            className={styles.formNumberPicker}
            type="inline"
            min={sendTotalCountMin}
            max={sendTotalCountMax}
            step={1}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => setData({ sendTotalCount })}
          />
          份
        </Form.Item>
        {hasProbability && (
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              name="probability"
              value={prizeData.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability)) {
                    setData({ probability });
                  }
                } else {
                  setData({ probability: '' });
                }
              }}
              className={styles.formInputCtrl}
            />

            <div style={{ marginTop: 5 }}>中奖概率不建议为0%，易引发客诉，请慎重</div>
          </Form.Item>
        )}
        {hasLimit && (
          <Form.Item label="每日限额" required requiredMessage="请输入每日限额">
            {prizeData.dayLimitType === 2 && <Input htmlType="hidden" name="dayLimit" value={prizeData.dayLimit} />}
            <div>
              <Radio.Group
                defaultValue={prizeData.dayLimitType}
                onChange={(dayLimitType) => setData({ dayLimitType, dayLimit: dayLimitType === 1 ? 0 : '' })}
              >
                <Radio id="1" value={1}>
                  不限制
                </Radio>
                <Radio id="2" value={2}>
                  限制
                </Radio>
              </Radio.Group>
              {prizeData.dayLimitType === 2 && (
                <div className={styles.panel}>
                  <NumberPicker
                    value={prizeData.dayLimit}
                    onChange={(dayLimit) => {
                      setData({ dayLimit });
                      field.setErrors({ dayLimit: '' });
                    }}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  份
                </div>
              )}
            </div>
          </Form.Item>
        )}
        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={prizeNameLength}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
          />
        </Form.Item>
        <Form.Item label="奖品图片">
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: equityImg });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
    </div>
  );
};

export default PropertyJdPoints;
