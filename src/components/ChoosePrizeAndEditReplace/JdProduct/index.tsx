import { Radio, Form, Button, NumberPicker, Input, Dialog, Grid, Field, Message } from '@alifd/next';
import React, { useState, useReducer, useEffect } from 'react';
import Plan from './components/Plan';
import delIcon from '../assets/del-icon.png';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import { activityEditDisabled, deepCopy, numRegularCheckInt } from '@/utils';
import { getResPrizeSku } from '@/api/prize';
import format from '@/utils/format';

interface ComponentProps {
  [propName: string]: any;
}

// eslint-disable-next-line complexity
const PropertyJdProduct = ({
  editValue,
  editOldValue,
  onChange,
  onCancel,
  hasProbability = true,
  hasLimit = true,
  hasOrderPrice = false,
  productImgTip = false,
  width,
  height,
  prizeNameLength,
  formData,
  sendTotalCountMin = 1,
  sendTotalCountMax = 999999999,
  hasEditPrize = true,
}: ComponentProps) => {
  const planImg = require('../assets/4.jpg');
  const defaultValue = {
    prizeKey: null,
    prizeType: 3,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: '',
  };
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const [oldPrizeData, setOldPrizeData] = useState(deepCopy(editOldValue || defaultValue));

  const field = Field.useField();

  const [winJdShow, setWinJdShow] = useState(false); // 京豆详情
  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  const submit = (values: any, errors: any): boolean | void => {
    if (prizeData.sendTotalCount > prizeData.quantityAvailable) {
      Message.error(`发放份数不能大于可用库存`);
      return false;
    }
    if (hasLimit && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }
    // if (hasProbability && +prizeData.probability <= 0) {
    //   Message.error(`中奖概率必须大于0`);
    //   return false;
    // }
    if (hasProbability && +prizeData.probability + +formData.totalProbability >= 100) {
      Message.error(`中奖总概率不能超过100`);
      return false;
    }
    !errors && onChange(prizeData);
  };
  const delPlan = () => {
    const data = { ...prizeData };
    data.prizeKey = null;
    data.prizeName = '';
    data.unitPrice = '';
    data.prizeImg = '';
    setPrizeData(data);
  };
  const onSubmit = (resource: any) => {
    if (!resource) {
      return;
    }
    resource.prizeKey = resource.skuCode;
    resource.prizeName = resource.skuName.substring(0, prizeNameLength);
    resource.prizeImg = resource.skuMainPicture;
    setPrizeData(resource);
    setWinJdShow(false);
    field.setErrors({ prizeKey: '', prizeName: '' });
    if (resource.prizeImg) field.setError('prizeImg', '');
  };
  const prizeFormLayout: any = {
    labelCol: {
      fixedSpan: 5,
    },
    colon: true,
  };

  // 目前存在老资产没有锁定库存的问题，暂时不获取可用库存
  // React.useEffect(() => {
  //   if (editValue) {
  //     getResPrizeSku({ skuCode: prizeData.skuCode }).then((res) => {
  //       if (res) {
  //         const data = { ...prizeData };
  //         data.quantityAvailable = +res.quantityTotal - +res.quantityFreeze + sendTotalCountMin;
  //         setPrizeData(data);
  //       }
  //     });
  //   }
  // }, []);
  return (
    <div className={styles.PropertyJdProduct}>
      <Form field={field} {...prizeFormLayout}>
        <Form.Item style={{ paddingTop: '15px' }} required requiredMessage="请选择实物">
          <Input htmlType="hidden" name="prizeKey" value={prizeData.prizeKey} />
          {!prizeData.prizeKey && (
            <div className={styles.selectActivity} style={{ marginTop: 10 }} onClick={() => setWinJdShow(true)}>
              <img style={{ width: '35%' }} src={planImg} alt="" />
            </div>
          )}
          {!!prizeData.prizeKey && (
            <div className={styles.beanPrizeBg}>
              {((activityEditDisabled() && hasEditPrize) || !activityEditDisabled()) && (
                <div
                  onClick={() => delPlan()}
                  style={{ backgroundImage: `url(${delIcon})` }}
                  className={styles.delIcon}
                />
              )}
              <div style={{ width: 210 }}>
                <p>商品名称：{prizeData.skuName}</p>
                <p>商品编号：{prizeData.skuCode}</p>
                <p>创建时间：{format.formatDateTimeDayjs(prizeData.createTime)}</p>
              </div>
              <div style={{ paddingLeft: 60 }}>
                <p>商家内部编号：{prizeData.wmsCode || '-'}</p>
                <p>可用库存：{prizeData.quantityAvailable}</p>
              </div>
            </div>
          )}
        </Form.Item>
        <Form.Item label="单份价值" required requiredMessage="请输入单份价值">
          <NumberPicker
            className={styles.formNumberPicker}
            onChange={(unitPrice: any) => setData({ unitPrice, unitCount: 1 })}
            name="unitPrice"
            type="inline"
            min={0.01}
            max={9999999}
            precision={2}
            value={prizeData.unitPrice}
          />
          元
        </Form.Item>
        {hasOrderPrice && (
          <Form.Item label="获奖消费额度" required requiredMessage="请输入获奖消费额度">
            <NumberPicker
              className={styles.formNumberPicker}
              onChange={(orderPrice: any) => setData({ orderPrice })}
              name="orderPrice"
              type="inline"
              min={0}
              max={9999999}
              precision={2}
              value={prizeData.orderPrice}
            />
            元
          </Form.Item>
        )}
        <Form.Item label="发放份数" required requiredMessage="请输入发放份数" validator={numRegularCheckInt}>
          <NumberPicker
            className={styles.formNumberPicker}
            type="inline"
            min={sendTotalCountMin}
            max={sendTotalCountMax}
            step={1}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => {
              if (
                activityEditDisabled() &&
                prizeData.operationType === 'edit' &&
                oldPrizeData.prizeKey === prizeData.prizeKey
              ) {
                if (sendTotalCount < oldPrizeData.sendTotalCount) {
                  Message.error('同一个奖品发放份数不能少于创建活动时发放份数');
                } else {
                  setData({ sendTotalCount });
                }
              } else {
                setData({ sendTotalCount });
              }
            }}
          />
          份
        </Form.Item>
        {hasProbability && (
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              name="probability"
              value={prizeData.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability)) {
                    setData({ probability });
                  }
                } else {
                  setData({ probability: '' });
                }
              }}
              className={styles.formInputCtrl}
            />

            <div style={{ marginTop: 5 }}>中奖概率不建议为0%，易引发客诉，请慎重</div>
          </Form.Item>
        )}
        {hasLimit && (
          <Form.Item label="每日限额" required requiredMessage="请输入每日限额">
            {prizeData.dayLimitType === 2 && <Input htmlType="hidden" name="dayLimit" value={prizeData.dayLimit} />}
            <div>
              <Radio.Group
                defaultValue={prizeData.dayLimitType || 1}
                onChange={(dayLimitType) => setData({ dayLimitType, dayLimit: dayLimitType === 1 ? 0 : '' })}
              >
                <Radio id="1" value={1}>
                  不限制
                </Radio>
                <Radio id="2" value={2}>
                  限制
                </Radio>
              </Radio.Group>
              {prizeData.dayLimitType === 2 && (
                <div className={styles.panel}>
                  <NumberPicker
                    value={prizeData.dayLimit}
                    onChange={(dayLimit) => {
                      setData({ dayLimit });
                      field.setErrors({ dayLimit: '' });
                    }}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  份
                </div>
              )}
            </div>
          </Form.Item>
        )}
        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={prizeNameLength}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
          />
        </Form.Item>
        <Form.Item label="奖品图片" required requiredMessage="请上传奖品图片">
          <Input htmlType="hidden" name="prizeImg" value={prizeData.prizeImg} />
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                  field.setErrors({ prizeImg: '' });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: prizeData.skuMainPicture ? prizeData.skuMainPicture : '' });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
          {productImgTip && <div className={styles.tip}>注：此图片仅用于兑换记录展示</div>}
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel} style={{ marginLeft: '10px' }}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <Dialog
        title="选择实物"
        footer={false}
        shouldUpdatePosition
        visible={winJdShow}
        onClose={() => setWinJdShow(false)}
      >
        <Plan onSubmit={onSubmit} />
      </Dialog>
    </div>
  );
};

export default PropertyJdProduct;
