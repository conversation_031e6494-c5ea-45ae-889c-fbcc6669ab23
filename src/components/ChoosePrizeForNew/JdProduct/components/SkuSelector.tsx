import React, { useEffect, useState } from 'react';
import { Form, Input, Table, Select } from '@alifd/next';
import styles from '../index.module.scss';
import LzImg from '@/components/LzImg';
import CONST from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { getSkuPageList } from '@/api/sku';

const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default ({ max = 1, value, filterList, getSelectList, mode = 'multiple' }) => {
  const [skuId, setSkuId] = useState('');
  const [skuName, setSkuName] = useState('');
  const [status, setStatus] = useState(1);
  const [loading, setLoading] = useState(false);
  const [pageInfo, setPage] = useState(defaultPage);
  const [list, setList] = useState<any[]>([]);
  const [selectedList, setSelected] = useState(value);
  const [selectedQuantity, setSelectedQuantity] = useState(0);
  // 选中的key列表
  const [selectedRowKeys, setSelectedRowKeys] = useState(value?.map((sku) => sku.skuId));

  const STATUS = [
    { label: '全部', value: 2 },
    { label: '下架', value: 0 },
    { label: '上架', value: 1 },
  ];

  const loadData = (query) => {
    setLoading(true);
    getSkuPageList(query)
      .then((res) => {
        setList(res.records!);
        const { pageNum, pageSize } = query;
        setPage({
          pageNum,
          pageSize,
          total: +res.total!,
        });
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 查询
  const handleSubmit = () => {
    loadData({ ...defaultPage, skuId, skuName, status });
    setPage(defaultPage);
  };

  // 重置
  const handleReset = () => {
    setSkuId('');
    setSkuName('');
    setStatus(1);
    loadData({ ...defaultPage, skuId: '', skuName: '', status: 1 });
    setPage(defaultPage);
  };

  // 换页
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    loadData({ pageSize, pageNum, skuId, skuName, status });
  };

  const rowSelection: any = {
    mode,
    onChange(keys, records) {
      const set = Array.from(new Set([...selectedList, ...records]));
      const data: any[] = [];
      const handleList: any[] = [];
      let flag = false;
      set.forEach((val) => {
        flag = false;
        handleList.forEach((item: { skuId: number }) => {
          if (item.skuId === val.skuId) {
            flag = true;
          }
        });
        if (!flag) {
          handleList.push(val);
        }
      });
      handleList.forEach((val) => {
        keys.forEach((item) => {
          if (val.skuId === item) {
            data.push(val);
          }
        });
      });
      setSelectedQuantity(data.length);
      getSelectList(data);
      setSelected(data);
      setSelectedRowKeys(keys);
    },
    selectedRowKeys,
  };

  useEffect(() => {
    loadData({ ...defaultPage, skuId, skuName, status });
  }, []);

  useEffect(() => {
    setSelectedRowKeys(filterList?.map((sku) => sku.skuId));
    setSelected(filterList);
    setSelectedQuantity(filterList.length);
  }, [filterList]);

  useEffect(() => {
    setSelectedRowKeys(value?.map((sku) => sku.skuId));
    setSelected(value);
    setSelectedQuantity(value.length);
  }, [value]);

  return (
    <div>
      {/* <LzTipPanel message="当日内新上架的商品，可在第二天后进行查看和选择，或直接自主导入商品即可" /> */}
      <Form colon inline>
        <Form.Item label="SKU编码">
          <Input
            style={{ width: '200px' }}
            name="skuId"
            value={skuId}
            maxLength={18}
            onChange={(v) => {
              const numericValue = v.replace(/\D/g, '');
              setSkuId(numericValue);
            }}
          />
        </Form.Item>
        <Form.Item label="商品名称">
          <Input
            style={{ width: '200px' }}
            name="skuName"
            value={skuName}
            onChange={(v) => {
              setSkuName(v);
            }}
          />
        </Form.Item>
        <Form.Item label="商品名称">
          <Select
            followTrigger
            name="status"
            defaultValue={1}
            mode="single"
            showSearch
            hasClear
            style={{ width: '200px' }}
            dataSource={STATUS}
            value={status}
            onChange={(v) => {
              setStatus(v);
            }}
          />
        </Form.Item>
        <Form.Item label="" style={{ width: '678px' }}>
          <div style={{ margin: '5px 0 20px 0' }}>
            已选择 <span style={{ color: '#39f' }}>{selectedQuantity}</span>/{max} 项
          </div>
        </Form.Item>
        <Form.Item label=" " colon={false}>
          <Form.Submit type="primary" validate onClick={handleSubmit} style={{ marginRight: 8 }}>
            查询
          </Form.Submit>
          <Form.Reset onClick={handleReset}>重置</Form.Reset>
        </Form.Item>
      </Form>
      <Table.StickyLock
        loading={loading}
        primaryKey="skuId"
        fixedHeader
        maxBodyHeight={270}
        dataSource={list}
        rowSelection={rowSelection}
      >
        <Table.Column
          title="商品信息"
          cell={(val, index, record) => (
            <div className={styles.part1} style={{ alignItems: 'center' }}>
              {record.skuMainPicture?.indexOf('360buyimg.com') > -1 ? (
                <LzImg style={{ width: 60, height: 60, marginRight: '5px' }} src={record.skuMainPicture} />
              ) : (
                <LzImg
                  style={{ width: 60, height: 60, marginRight: '5px' }}
                  src={`${CONST.IMAGE_PREFIX}${record.skuMainPicture}`}
                />
              )}
              <div>
                <div className={styles.part1_p1}>{record.skuName}</div>
                <div className={styles.part1_p2}>SKU编码：{record.skuId}</div>
              </div>
            </div>
          )}
        />
        <Table.Column
          width={120}
          title="京东价（元）"
          cell={(val, index, info) => <span>{new Intl.NumberFormat().format(info.jdPrice)}</span>}
        />
      </Table.StickyLock>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
