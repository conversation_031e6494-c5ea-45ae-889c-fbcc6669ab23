// import { get<PERSON>phy<PERSON><PERSON>InfoL<PERSON>, get<PERSON><PERSON><PERSON><PERSON>, getZphyMemberLevel } from '@/api/zphy';
import { memberDetail, memberGetMemberLevel } from '@/api/firmus';
import { decorateReducer, initialState } from '@/pages/feihe/memberCenterNew/decorate/decorateReducer';
import { deepCopy } from '@/utils';
import { Dialog, Loading, Message, Tab } from '@alifd/next';
import isArray from 'lodash/isArray';
import React, { useReducer } from 'react';
import DecorateComponentArea from './DecorateComponentArea';
import PreviewArea from './PreviewArea';
import styles from './index.module.scss';

export default () => {
  const [state, dispatch] = useReducer(decorateReducer, initialState());
  const [init, setInit] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  function convertStringsToJSON(objMap: any) {
    return Object.fromEntries(
      Object.entries(objMap).map(([key, value]) => [key, value ? JSON.parse(value as string) : value]),
    );
  }

  const resetInitData = (result) => {
    return result;
  };
  const getInitData = async () => {
    setLoading(true);
    try {
      const { content } = await memberDetail();
      const memberLevelsRes = await memberGetMemberLevel();
      const result = resetInitData(convertStringsToJSON(content));
      result.memberLevels = memberLevelsRes;
      console.log('🚀 ~ getInitData ~ result:', { ...result });
      dispatch({
        type: 'INIT_MODULE',
        payload: { ...result },
      });
      setInit(true);
      setLoading(false);
    } catch (e) {
      setLoading(false);
      Message.error(e.message);
    }
  };
  React.useEffect(() => {
    getInitData().then();
  }, []);
  return (
    <div>
      <div id="main">
        <Loading visible={loading} style={{ width: '100%', minHeight: '80vh' }}>
          {init && (
            <div className={styles.mainContainer}>
              <PreviewArea state={state} dispatch={dispatch} />
              {/* 添加console用于调试数据更新 */}
              <DecorateComponentArea selectedModule={state.selectedModule} state={state} dispatch={dispatch} />
            </div>
          )}
        </Loading>
      </div>
    </div>
  );
};
