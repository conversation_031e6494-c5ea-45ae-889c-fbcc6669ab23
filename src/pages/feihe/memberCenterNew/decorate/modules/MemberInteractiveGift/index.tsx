import { save<PERSON><PERSON><PERSON><PERSON> } from '@/api/zphy';

import LzDialog from '@/components/LzDialog';
import LzImageSelector from '@/components/LzImageSelector';
import LzTipPanel from '@/components/LzTipPanel';
import EditHotZone from '@/pages/decorate/compoonets/EditHotZone';
import SettingPeople from '@/pages/decorate/compoonets/SettingPeople';
import SortButtons from '@/pages/mengniu/components/SortButtons';
import { Button, Card, Divider, Message } from '@alifd/next';
import React from 'react';
import styles from './index.module.scss';

const TIP = (
  <div>
    <div style={{ fontWeight: 'bold', fontSize: 14, marginBottom: 5 }}>说明: </div>
    <div>大小不超过1M图片类型为jpg、png</div>
  </div>
);

export default ({ data, data: dataList, dispatch, allJson }) => {
  const [pageLoading, setPageLoading] = React.useState(false);
  const [visible, setVisible] = React.useState(false);
  const [peopleVisible, setPeopleVisible] = React.useState(false);
  const [editData, setEditData] = React.useState({});
  const [editDataIndex, setEditDataIndex] = React.useState(0);

  const deleteHotZoneUrl = (dataIndex, hotZoneIndex) => {
    dataList[dataIndex].hotZoneList.splice(hotZoneIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: [...dataList] });
  };

  const deleteHotZone = (dataIndex) => {
    dataList.splice(dataIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: [...dataList] });
  };

  const addHotZone = (dataIndex) => {
    const data = dataList[dataIndex];
    if (!data.bg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setEditData(data);
    setEditDataIndex(dataIndex);
    setVisible(true);
  };

  const handleAddImage = () => {
    const updatedDataList = [...dataList];
    updatedDataList.push({ bg: '', hotZoneList: [] });
    dispatch({ type: 'UPDATE_MODULE', payload: updatedDataList });
  };
  const isValidSwiper = () => {
    let err = false;

    dataList.forEach((e) => {
      if (!e.bg) {
        err = true;
      }
      if (!e.hotZoneList.length) {
        err = true;
      }
    });
    if (err) {
      return false;
    }
    return true;
  };
  const saveSetting = (): any => {
    if (!dataList.length) {
      Message.error('请至少添加一张图片');
      return false;
    }
    if (!isValidSwiper()) {
      Message.error('请完善热区信息');
      return false;
    }
    let updateJson = {};
    if (allJson.showZhuoRui) {
      updateJson = {
        ...allJson,
        ZRNewMember: {
          ...allJson.ZRNewMember,
          MemberInteractiveGift: [...dataList],
        },
        ZROldMember: {
          ...allJson.ZROldMember,
          MemberInteractiveGift: [...dataList],
        },
      };
    } else {
      updateJson = {
        ...allJson,
        NewMember: {
          ...allJson.NewMember,
          MemberInteractiveGift: [...dataList],
        },
        OldMember: {
          ...allJson.OldMember,
          MemberInteractiveGift: [...dataList],
        },
      };
    }
    console.log('updateJson', updateJson);
    const params = {
      json: JSON.stringify(updateJson),
      type: 1,
      version: allJson.version,
    };
    setPageLoading(true);
    saveZphyJson(params).then((res) => {
      Message.success('保存成功');
      setPageLoading(false);
    });
  };

  const commonProps = {
    title: '备用互动热区',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };

  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <LzTipPanel message={TIP} />
          <Button className={styles.addBtn} onClick={handleAddImage}>
            添加图片
          </Button>

          <div className={styles.operation}>
            {dataList.map((data, dataIndex) => (
              <div className={styles.hotContainer} key={dataIndex}>
                <div
                  style={{
                    position: 'absolute',
                    right: 20,
                    top: 10,
                  }}
                >
                  <SortButtons
                    list={dataList}
                    index={dataIndex}
                    hasDelete
                    onUpdateList={(newList) => {
                      dispatch({ type: 'UPDATE_MODULE', payload: newList });
                    }}
                  />
                </div>
                <div className={styles.imgUpload}>
                  <div>
                    <LzImageSelector
                      // width={750}
                      // bgWidth={158}
                      value={data.bg}
                      onChange={(bg) => {
                        const updatedDataList = [...dataList];
                        updatedDataList[dataIndex].bg = bg;
                        updatedDataList[dataIndex].hotZoneList = [];
                        dispatch({ type: 'UPDATE_MODULE', payload: updatedDataList });
                      }}
                    />
                  </div>
                  <div className={styles.setting}>
                    <Button className={styles.btn} onClick={() => addHotZone(dataIndex)}>
                      添加热区
                    </Button>
                    {data.hotZoneList.map((item, hotZoneIndex) => (
                      <div key={hotZoneIndex} className={styles.urlContainer}>
                        <div className={styles.url}>{`热区0${hotZoneIndex + 1}：${item.url}`}</div>
                        <i
                          className="iconfont icon-icon-07 btn-del"
                          onClick={() => deleteHotZoneUrl(dataIndex, hotZoneIndex)}
                        />
                      </div>
                    ))}
                  </div>
                  <div className={styles.removeHotZone}>
                    <i className="iconfont icon-icon-07 btn-del" onClick={() => deleteHotZone(dataIndex)} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>
      <LzDialog
        title={'编辑热区'}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '750px' }}
      >
        <EditHotZone
          data={editData}
          dataList={dataList}
          dataIndex={editDataIndex}
          dispatch={dispatch}
          onClose={() => setVisible(false)}
        />
      </LzDialog>
      <LzDialog
        title={'人群限制'}
        visible={peopleVisible}
        footer={false}
        onClose={() => setPeopleVisible(false)}
        style={{ width: '500px' }}
      >
        <SettingPeople
          dataList={dataList}
          dataIndex={editDataIndex}
          dispatch={dispatch}
          onClose={() => setPeopleVisible(false)}
        />
      </LzDialog>
    </div>
  );
};
