$px-scale: 20;
.preview {
  width: 100%;
  position: relative;
  border-radius: 8.5px;
  overflow: hidden;
  .member-info {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100% auto;
    padding: 9.45px * $px-scale 0.47px * $px-scale 0.5px * $px-scale;
  }
  .nick-level {
    display: flex;
    align-items: center;
    padding-left: 0.3px * $px-scale;
    .text-1 {
      font-size: 13px;
      background-image: linear-gradient(to right, #56351e, #8b5609);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .text-level {
      width: 100px * 0.95;
      height: 36.6px * 0.95;
      margin: 0 0 0.1px * $px-scale 0.1px * $px-scale;
    }

    .text-name {
      max-width: 5px * $px-scale;
      padding: 0 0.15px * $px-scale 0 0;
      overflow: hidden;
      font-size: 17px;
      white-space: nowrap;
      text-overflow: ellipsis;
      background-image: linear-gradient(to right, #653a1f, #55351e);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .text-2 {
      padding: 0 0 0 0.02px * $px-scale;
      font-size: 13px;
      background-image: linear-gradient(to right, #8c5007, #ac6100, #55351e);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .welcome-text {
    padding: 0.6px * $px-scale 0 0.6px * $px-scale 0.3px * $px-scale;
    font-size: 10px;
    background-image: linear-gradient(to right, #8c5007, #ac6100, #55351e);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .order-count-container {
    position: relative;
    display: flex;

    .order-count {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-left: 0.3px * $px-scale;

      &:first-child {
        margin-right: 2.2px * $px-scale;
      }
    }

    .count-text {
      display: flex;
      align-items: center;
      color: #503622;
      font-weight: bolder;
      font-size: 1.3px * $px-scale;

      .num {
        margin-right: 0.2px * $px-scale;
      }
    }

    .change-zr-button {
      position: absolute;
      top: -0.5px * $px-scale;
      right: 0.2px * $px-scale;
      width: 135px;
      height: 49px;
      background: url(https://img10.360buyimg.com/imgzone/jfs/t1/236669/17/11086/3258/65ae6629F24ad15e8/63ad2dea33411399.png)
        no-repeat center;
      background-size: 100% 100%;

      .zr-order-count {
        position: absolute;
        right: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 2.9px * $px-scale;
        height: 100%;
        padding-top: 0.5px * $px-scale;

        .text {
          color: #503622;
          font-size: 10px;
        }

        .num {
          color: #503622;
          font-weight: bolder;
          font-size: 1.2px * $px-scale;
        }
      }
    }

    .count-tips {
      position: relative;
      display: flex;
      align-items: center;
      font-size: 1.9px * $px-scale;
      letter-spacing: 0.04px * $px-scale;
    }

    .count-tips-text {
      margin-right: 4px;
      color: #503622;
      font-size: 9px;
    }

    .count-tips-icon {
      position: absolute;
      top: 0;
      right: -0.25px * $px-scale;
      width: 9px;
      height: 9px;
    }
  }
  .member-level-progress-container {
    display: flex;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    padding: 1.5px * $px-scale 0.3px * $px-scale 0.7px * $px-scale 0;
  }

  .member-level-progress-bg {
    position: relative;
    width: 100%;
    height: 0.35px * $px-scale;
    background: url('https://img10.360buyimg.com/imgzone/jfs/t1/227043/14/10388/1723/65892b65Fcf8537f5/3822f6fdb0d65055.png')
      no-repeat center;
    background-size: 100% 100%;
    border-radius: 0.2px * $px-scale;
  }

  .member-level-progress {
    position: absolute;
    top: 50%;
    left: 0;
    height: 98%;
    background-color: #eedfc4;
    border-radius: 6px;
    transform: translateY(-50%);
  }

  .member-level-info {
    position: absolute;
    top: -0.3px * $px-scale;
    z-index: 2;
  }

  .member-level-star-icon {
    width: 0.9px * $px-scale;
    height: 0.9px * $px-scale;
    background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/243567/11/830/5063/658a6937Ff7bc6e3e/836ba576b89d8d78.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 88% 88%;
    border-radius: 50%;

    &-active {
      background-image: url('https://img10.360buyimg.com/imgzone/jfs/t1/225832/23/10481/8842/658a6937F91073ea3/bb8db068ff15498b.png');
    }
  }

  .member-level-icon {
    position: absolute;
    bottom: 1px * $px-scale;
    left: -0.15px * $px-scale;
    width: 1.25px * $px-scale * 1.2;
    height: 0.5px * $px-scale * 1.2;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% auto;
  }

  .member-level-count {
    position: absolute;
    bottom: -0.6px * $px-scale;
    left: 50%;
    color: #503622;
    font-size: 0.45px * $px-scale;
    transform: translateX(-50%);
    word-break: keep-all;
  }
}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .kvContainer {
    padding: 15px;
    border-radius: 5px;
    .imgUpload {
      display: flex;
      align-items: center;
      .tip {
        margin-left: 10px;
      }
    }
  }
  .colorContainer {
    padding: 15px;
    border-radius: 5px;
    .colorPicker {
      display: flex;
      margin-top: 10px;
      .colorItem {
        margin-right: 20px;
        span {
          font-weight: bold;
          margin-right: 10px;
        }
      }
    }
  }
  .popupContainer{
    padding: 15px;
    border-radius: 5px;
  }
  .btnContainer {
    padding: 15px;
    .imgUpload {
      display: flex;
      align-items: center;
      .tip {
        margin-left: 10px;
      }
    }
    .btnLink {
      margin-top: 10px;
      input {
        width: 500px;
      }
    }
  }
  .memberContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
    .levelSetting {
      .flex-column {
        display: flex;
        justify-content: center;
      }
    }
  }
}
.addBtn {
  width: 100%;
  margin-top: -5px;
  margin-bottom: 10px;
}
.imgUpload {
  display: flex;

  .tip {
    margin-top: 10px;
  }

  .removeHotZone {
    margin-top: 4px;
    i {
      margin-left: 5px;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        color: #1677ff;
      }
    }
  }
}
.setting {
  margin-left: 15px;
  .btn {
    width: 300px;
  }
  .urlContainer {
    width: 300px;
    height: 28px;
    background: #fff;
    display: flex;
    align-items: center;
    padding: 0 10px;
    border-radius: 5px;
    margin-top: 8px;
    position: relative;
    .url {
      width: 100%;
      display: flex;
      overflow-x: scroll;
      white-space: nowrap;
    }
    .url::-webkit-scrollbar {
      display: none;
    }

    i {
      position: absolute;
      right: -17px;
      top: 5px;
      cursor: pointer;
      font-size: 12px;
    }
  }
}
