import { saveZ<PERSON><PERSON><PERSON>, saveZphyMemberLevel } from '@/api/zphy';
import LzImageSelector from '@/components/LzImageSelector';
import LzColorPicker from '@/components/LzColorPicker';
import LzTipPanel from '@/components/LzTipPanel';
import LzDialog from '@/components/LzDialog';
import EditHotZone from '../../../decorate/compoonets/EditHotZone';
import { checkUrl } from '@/pages/mengniu/utils';
import {
  Box,
  Button,
  Card,
  Divider,
  Field,
  Form,
  Input,
  Message,
  NumberPicker,
  Radio,
  Switch,
  Table,
} from '@alifd/next';
import React from 'react';
import styles from './index.module.scss';

const formItemLayout = {
  labelCol: {
    span: 2,
  },
  wrapperCol: {
    span: 22,
  },
  labelAlign: 'left',
  colon: true,
};

const validateNavUrl = (rule, value, callback) => {
  if (!value) {
    callback();
  } else {
    try {
      if (checkUrl(value)) {
        callback(`链接必须以 jd.com 或 isvjcloud.com 结尾`);
      } else {
        callback();
      }
    } catch (error) {
      callback(`请输入有效的 URL`);
    }
  }
};

function MemberCard({ data, dispatch, allJson, defaultData }) {
  console.log('MemberCard', data);
  const [pageLoading, setPageLoading] = React.useState(false);
  const [keyName, setKeyName] = React.useState('');
  const [imgName, setImgName] = React.useState('');
  const [hotVisible, setHotVisible] = React.useState(false);
  const [visible, setVisible] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };

  React.useEffect(() => {}, []);
  const field = Field.useField();
  const addRepeatHotZone = () => {
    if (!data.repeatBg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setKeyName('repeatHotZoneList');
    setImgName('repeatBg');
    setHotVisible(true);
  };

  const deleteRepeatHotZoneUrl = (hotZoneIndex) => {
    data.repeatHotZoneList.splice(hotZoneIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: data });
  };
  const isValidMemberLevels = (levels) => {
    const res = levels.filter((v, i) => i > 2 && v.minCans <= levels[i - 1].minCans);
    if (res.length > 0) {
      return false;
    }
    return true;
  };
  const saveSetting = (): any => {
    if (!data.moduleBgImg) {
      Message.error('请上传kv图');
      return false;
    }
    let updateJson = {};
    console.log('updateJson', updateJson);
    const params = {
      json: JSON.stringify(updateJson),
      type: 1,
      version: allJson.version,
    };
    setPageLoading(true);
    saveZphyJson(params)
      .then((res) => {
        Message.success('保存成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };
  const saveSetting1 = (): any => {
    if (!isValidMemberLevels(data.memberLevels)) {
      Message.error('请检查会员等级金额,高等级起始金额不能低于低等级起始金额');
      return false;
    }
    saveZphyMemberLevel({ memberLevelRequests: data.memberLevels }).then((res) => {
      Message.success('保存成功');
      dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
    });
  };
  const saveSetting2 = (): any => {
    saveZphyMemberLevel({ memberLevelRequests: data.memberLevels }).then((res) => {
      Message.success('保存成功');
      dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
    });
  };
  const commonProps = {
    title: '会员卡',
    extra: (
      <div>
        {/* <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
          更新至当前设置
        </Button> */}
      </div>
    ),
  };

  const calcPotNumber = (index) => {
    if (index == data.memberLevels.length - 1) {
      return '';
    }
    return `< ${Number(data.memberLevels[index + 1].minCans)}`;
  };
  return (
    <div>
      <Card free style={{ marginBottom: 10 }}>
        <Card.Header
          title="公告弹窗"
          extra={
            <Box direction="row" align="center">
              {/* <LzTipPanel
                warning
                // message="注：本弹窗不区分卓睿非卓睿页面，一经修改，保存发布后在所有页面生效"
                style={{
                  marginBottom: 0,
                  marginRight: 20,
                }}
              /> */}
              <Button
                type="primary"
                onClick={() => {
                  field.validate((errors, values) => {
                    if (errors) {
                      return;
                    }
                    saveSetting();
                  });
                }}
                disabled={pageLoading}
              >
                更新至当前设置
              </Button>
            </Box>
          }
        />
        <Divider />
        <Card.Content>
          <Form field={field} {...formItemLayout}>
            <Form.Item
              label="是否弹出"
              extra={
                <div className="next-form-item-extra">
                  注：选择弹出，每次进入会员中心，弹出该弹窗（切换卓睿/非卓睿时不会弹出）
                </div>
              }
            >
              <Switch
                checked={data.adDialog.showAd}
                onChange={(value) => {
                  setData({
                    adDialog: {
                      ...data.adDialog,
                      showAd: value,
                    },
                  });
                }}
              />
            </Form.Item>
            <Form.Item label="图片上传">
              <Box>
                <Box direction="row" align="center">
                  <LzImageSelector
                    value={data.adDialog.adImgUrl}
                    onChange={(value) => {
                      setData({
                        adDialog: {
                          ...data.adDialog,
                          adImgUrl: value,
                        },
                      });
                    }}
                  />
                </Box>
                <div className={styles.colorPicker} style={{ marginTop: 20 }}>
                  <Button
                    style={{ marginLeft: '35px' }}
                    type={'primary'}
                    text
                    onClick={() => {
                      setData({
                        adDialog: {
                          ...data.adDialog,
                          adImgUrl: defaultData.MemberCard.adDialog.adImgUrl,
                        },
                      });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Box>
            </Form.Item>
            <Form.Item
              name="adLink"
              label="配置链接"
              validator={(rule, value, callback) => validateNavUrl(rule, value, callback)}
            >
              <Input
                value={data.adDialog.adLink}
                trim
                placeholder="请输入跳转链接"
                onChange={(value) => {
                  setData({
                    adDialog: {
                      ...data.adDialog,
                      adLink: value,
                    },
                  });
                }}
              />
            </Form.Item>
          </Form>
        </Card.Content>
      </Card>
      <Card free style={{ marginBottom: 10 }}>
        <Card.Header title="kv" />
        <Divider />
        <Card.Content>
          <div className={styles.kvContainer}>
            <div className="crm-label">kv设置</div>
            <div className={styles.operation}>
              <div className={styles.MemberContainer}>
                <div className={styles.imgUpload}>
                  <div>
                    <LzImageSelector
                      bgWidth={266}
                      bgHeight={150}
                      value={data.repeatBg}
                      onChange={(repeatBg) => {
                        const updatedData = { ...data };
                        updatedData.repeatBg = repeatBg;
                        setData(updatedData);
                      }}
                    />
                    <div className={styles.tip}>
                      {/* <div>图片尺寸：建议宽度为707px，高度为401px</div> */}
                      <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                    </div>
                  </div>
                  <div className={styles.setting}>
                    <Button className={styles.btn} onClick={() => addRepeatHotZone()}>
                      添加热区
                    </Button>
                    {Array.isArray(data.repeatHotZoneList) &&
                      data.repeatHotZoneList.map((hot, hotZoneIndex) => (
                        <div key={hotZoneIndex} className={styles.urlContainer}>
                          <div className={styles.url}>{`热区${String(hotZoneIndex + 1).padStart(2, '0')}:${
                            hot.url
                          }`}</div>
                          <i
                            className="iconfont icon-icon-07 btn-del"
                            onClick={() => deleteRepeatHotZoneUrl(hotZoneIndex)}
                          />
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>
      <Card free style={{ marginBottom: 10 }}>
        <Card.Header title="等级配置" />
        <Divider />
        <Card.Content>
          <div className={styles.memberContainer}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div className="crm-label">等级对应金额设置</div>
              <Button type="primary" onClick={saveSetting1}>
                更新至当前设置
              </Button>
            </div>
            <div style={{ color: '#e75050' }}>
              <div>*等级金额档位修改后生效时间：</div>
              <div>
                *0点-12点进行修改，保存后当日16点后全部会员生效；12点-24点进行修改，保存后次日4点后全部会员生效。
              </div>
              <div>生效期间，会员分批变化等级，期间领取等级礼以当时等级为准。建议根据以上规则修改前进行公告。</div>
            </div>
            <div className={styles.levelSetting}>
              <Table dataSource={data.memberLevels} style={{ marginTop: '15px' }} loading={loading}>
                <Table.Column title="等级" align={'center'} cell={(val, index, info) => <span>LV{info.level}</span>} />
                <Table.Column
                  title="描述及当前设置"
                  align={'center'}
                  cell={(_, index, row) => {
                    return (
                      <div>
                        {row.level == 0 ? (
                          <div>新客未购</div>
                        ) : row.level == 1 ? (
                          <div>只购买过中小罐未购买正装的会员</div>
                        ) : (
                          <div className={styles['flex-column']}>
                            <div style={{ color: '#1677ff' }}>{row.minCans}&nbsp;</div>
                            <div>{`≤ 正装金额`}&nbsp;</div>
                            <div>{calcPotNumber(index)}</div>
                          </div>
                        )}
                      </div>
                    );
                  }}
                />
                <Table.Column
                  title="修改等级起始金额"
                  align={'center'}
                  cell={(_, index, row) => (
                    <div>
                      {row.level < 3 ? (
                        <div />
                      ) : (
                        <NumberPicker
                          style={{ width: '150px' }}
                          value={row.minCans}
                          onChange={(v) => {
                            console.log(v);
                            if (!v) {
                              row.minCans = 2;
                            } else {
                              row.minCans = v;
                            }
                            setData({ memberLevels: data.memberLevels });
                          }}
                          type="inline"
                          min={2}
                          max={9999999}
                        />
                      )}
                    </div>
                  )}
                />
              </Table>
            </div>
          </div>
        </Card.Content>
      </Card>
      <Card free style={{ marginBottom: 10 }}>
        <Card.Header
          {...commonProps}
          extra={
            <Box direction="row" align="center">
              <Button
                type="primary"
                onClick={() => {
                  saveSetting2();
                }}
                disabled={pageLoading}
              >
                更新至当前设置
              </Button>
            </Box>
          }
        />
        <Divider />
        <Card.Content>
          <div className={styles.operation}>
            <div className={styles.kvContainer}>
              <div className="crm-label">卡面背景</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  width={742}
                  height={719}
                  value={data.moduleBgImg}
                  onChange={(moduleBgImg) => {
                    setData({ moduleBgImg });
                  }}
                />
                <div className={styles.tip}>
                  <div>图片尺寸：宽度建议为750px,支持jpg、jpeg、png格式，大小不超过1M</div>
                </div>
              </div>
            </div>
            <div className={styles.colorContainer}>
              <div className="crm-label">文字颜色设置</div>
              <div className={styles.colorPicker}>
                <div className={styles.colorItem}>
                  <span>用户等级颜色:</span>
                  <LzColorPicker
                    value={data.levelColor}
                    onChange={(levelColor) => {
                      const updatedData = { ...data };
                      updatedData.levelColor = levelColor;
                      setData(updatedData);
                    }}
                  />
                </div>
                <div className={styles.colorItem}>
                  <span>用户昵称颜色:</span>
                  <LzColorPicker
                    value={data.nickColor}
                    onChange={(nickColor) => {
                      const updatedData = { ...data };
                      updatedData.nickColor = nickColor;
                      setData(updatedData);
                    }}
                  />
                </div>
                <div className={styles.colorItem}>
                  <span>卡面文字颜色:</span>
                  <LzColorPicker
                    value={data.textColor}
                    onChange={(textColor) => {
                      const updatedData = { ...data };
                      updatedData.textColor = textColor;
                      setData(updatedData);
                    }}
                  />
                </div>
              </div>
            </div>
            <div className={styles.popupContainer}>
              <div className="crm-label">提示弹窗</div>
              <LzImageSelector
                width={742}
                height={719}
                value={data.moduleBgImg}
                onChange={(moduleBgImg) => {
                  setData({ moduleBgImg });
                }}
              />
              <div className={styles.tip}>
                <div>图片尺寸：宽度建议为650px,支持jpg、jpeg、png格式，大小不超过1M</div>
              </div>
            </div>
            <div className={styles.btnContainer}>
              <div className="crm-label">标题配置</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  value={data.btnImg}
                  onChange={(btnImg) => {
                    const updatedData = { ...data };
                    updatedData.btnImg = btnImg;
                    setData(btnImg);
                  }}
                />
              </div>
              <div className={styles.btnLink}>
                标题链接：
                <Input
                  value={data.btnLink}
                  trim
                  placeholder="请输入跳转链接"
                  onChange={(value) => {
                    setData({
                      btnLink: value,
                    });
                  }}
                />
              </div>
            </div>
            <div className={styles.btnContainer}>
              <div className="crm-label">跳转按钮配置</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  width={742}
                  height={719}
                  value={data.btnImg}
                  onChange={(btnImg) => {
                    const updatedData = { ...data };
                    updatedData.btnImg = btnImg;
                    setData(btnImg);
                  }}
                />
                <div className={styles.tip}>
                  <div>图片尺寸：为742*719px,支持jpg、jpeg、png格式，大小不超过1M</div>
                </div>
              </div>
              <div className={styles.btnLink}>
                按钮链接：
                <Input
                  value={data.btnLink}
                  trim
                  placeholder="请输入跳转链接"
                  onChange={(value) => {
                    setData({
                      btnLink: value,
                    });
                  }}
                />
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>
      <Card free>
        <Card.Header
          title="活动配置"
          extra={
            <Box direction="row" align="center">
              <Button
                type="primary"
                onClick={() => {
                  saveSetting2();
                }}
                disabled={pageLoading}
              >
                更新至当前设置
              </Button>
            </Box>
          }
        />
        <Divider />
        <Card.Content>
          {(() => {
            // tabList 结构修改为对象数组
            const tabList = [
              { key: 0, label: 'LV.0' },
              { key: 1, label: 'LV.1' },
              { key: 2, label: 'LV.2' },
              { key: 3, label: 'LV.3' },
              { key: 4, label: 'LV.4' },
              { key: 5, label: 'LV.5' },
            ];
            // 假设 activityConfig 是你的活动配置数据
            const activityConfig = [
              {
                activityList: [
                  {
                    activityType: '1',
                    linkUrl: '',
                    prizeContent: {},
                    showImage: '',
                    showType: '1',
                  },
                ],
                level: 0,
              },
              // 其他等级...
            ];
            const [activeTab, setActiveTab] = React.useState(0);

            // 根据选中的 tab key 查找对应 level 的 activityList
            const currentLevel = activityConfig.find((item) => item.level === tabList[activeTab].key) || {
              activityList: [],
            };
            const activityList = currentLevel.activityList;

            return (
              <div>
                {/* Tab栏 */}
                <div style={{ display: 'flex', gap: 24, marginBottom: 16 }}>
                  {tabList.map((tab, idx) => (
                    <span
                      key={tab.key}
                      style={{
                        color: idx === activeTab ? '#1677ff' : '#333',
                        fontWeight: idx === activeTab ? 700 : 400,
                        borderBottom: idx === activeTab ? '2px solid #1677ff' : 'none',
                        cursor: 'pointer',
                        fontSize: 16,
                        padding: '0 8px',
                      }}
                      onClick={() => setActiveTab(idx)}
                    >
                      {tab.label}
                    </span>
                  ))}
                  <Button type="primary" style={{ marginLeft: 'auto' }} size="small">
                    添加
                  </Button>
                  <span style={{ color: '#1677ff', marginLeft: 8 }}>（{activityList.length}/6）</span>
                </div>
                {/* 当前Tab下的活动内容 */}
                {activityList.length === 0 && (
                  <div style={{ color: '#999', textAlign: 'center', padding: 32 }}>暂无活动</div>
                )}
                {activityList.map((item, idx) => (
                  <div
                    key={idx}
                    style={{
                      background: '#fff',
                      borderRadius: 8,
                      marginBottom: 24,
                      boxShadow: '0 2px 8px #f0f1f2',
                      padding: 20,
                      position: 'relative',
                    }}
                  >
                    {/* 删除按钮 */}
                    <Button text style={{ position: 'absolute', top: 16, right: 16, color: '#1677ff' }} icon="delete">
                      删除
                    </Button>
                    <div style={{ marginBottom: 16 }}>
                      <span style={{ fontWeight: 600, marginRight: 16 }}>活动类型：</span>
                      <Radio.Group
                        value={item.activityType}
                        onChange={(v) => {
                          item.activityType = v;
                          setData({ ...data }); // 触发数据更新
                        }}
                      >
                        <Radio value="1">升级礼</Radio>
                        <Radio value="2">等级券包</Radio>
                        <Radio value="3">活动跳转</Radio>
                      </Radio.Group>
                    </div>
                    <div style={{ marginBottom: 16 }}>
                      <span style={{ fontWeight: 600, marginRight: 16 }}>显示类型：</span>
                      <Radio.Group
                        value={item.showType}
                        onChange={(v) => {
                          item.showType = v;
                          setData({ ...data }); // 触发数据更新
                        }}
                      >
                        <Radio value="1">一排一</Radio>
                        <Radio value="2">一排二</Radio>
                        <Radio value="3">一排三</Radio>
                      </Radio.Group>
                    </div>
                    {/* 图片上传 */}
                    <div style={{ marginBottom: 16 }}>
                      <span style={{ fontWeight: 600, marginRight: 16 }}>图片上传：</span>
                      {item.showImage ? (
                        <img src={item.showImage} alt="" style={{ width: 120, height: 80, objectFit: 'cover' }} />
                      ) : (
                        <span style={{ color: '#ccc' }}>暂无图片</span>
                      )}
                      <span style={{ color: '#999', marginLeft: 16 }}>
                        图片尺寸：xx*xxpx，支持png、jpg、jpeg格式，大小不超过1M
                      </span>
                    </div>
                    {/* 配置链接 */}
                    <div style={{ marginBottom: 16 }}>
                      <span style={{ fontWeight: 600, marginRight: 16 }}>配置链接：</span>
                      <Input style={{ width: 300 }} value={item.linkUrl} placeholder="请输入跳转链接" />
                    </div>
                    {/* 奖品内容（prizeContent）可根据实际结构补充渲染 */}
                  </div>
                ))}
              </div>
            );
          })()}
        </Card.Content>
      </Card>
      <LzDialog
        title={'编辑热区'}
        visible={hotVisible}
        footer={false}
        onClose={() => setHotVisible(false)}
        style={{ width: '750px' }}
      >
        <EditHotZone
          keyName={keyName}
          imgName={imgName}
          data={data}
          dataList={data.hotDataList}
          dataIndex={0}
          dispatch={({ type, payload }) => dispatch({ type, payload: payload[0] })}
          onClose={() => setHotVisible(false)}
        />
      </LzDialog>
    </div>
  );
}

export default MemberCard;
