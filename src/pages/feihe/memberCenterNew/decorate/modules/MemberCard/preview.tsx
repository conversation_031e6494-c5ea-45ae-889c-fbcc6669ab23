import React from 'react';
import styles from './index.module.scss';

export default ({ data, allJson }) => {
  const currentLevel = 1;
  const LevelImageMap = {
    0: 'https://img10.360buyimg.com/imgzone/jfs/t1/227929/20/11033/5621/658d24a8F428a64a9/7a98f86b99a2ca71.png',
    1: 'https://img10.360buyimg.com/imgzone/jfs/t1/240026/8/1378/3338/658d24a8F9a610cf5/a27d6e6595557009.png',
    2: 'https://img10.360buyimg.com/imgzone/jfs/t1/243193/32/1153/3399/658d24a8F297f961b/09268c567b2b8996.png',
    3: 'https://img10.360buyimg.com/imgzone/jfs/t1/248678/14/989/5587/658d24a8F32c7988b/1761706231d24a02.png',
    4: 'https://img10.360buyimg.com/imgzone/jfs/t1/94972/8/32134/3192/658d24a9F6237ea87/7651ea7b32c7ec02.png',
    5: 'https://img10.360buyimg.com/imgzone/jfs/t1/245741/28/1117/5561/658d24a9F0fe4a85b/adcd0bca86bc451b.png',
    6: 'https://img10.360buyimg.com/imgzone/jfs/t1/245949/5/1115/5800/658d24a9F18a0145e/daa03bf8671f9169.png',
  };
  const levelList = [
    {
      level: 2,
      levelCount: 1,
      levelIcon: LevelImageMap[2],
      percent: 10.7,
      progressWidth: 14,
    },
    {
      level: 3,
      levelCount: 8,
      levelIcon: LevelImageMap[3],
      percent: 29.3,
      progressWidth: 32,
    },
    {
      level: 4,
      levelCount: 18,
      levelIcon: LevelImageMap[4],
      percent: 47.8,
      progressWidth: 50,
    },
    {
      level: 5,
      levelCount: 28,
      levelIcon: LevelImageMap[5],
      percent: 66.6,
      progressWidth: 69,
    },
    {
      level: 6,
      levelCount: 40,
      levelIcon: LevelImageMap[6],
      percent: 84.8,
      progressWidth: 87,
    },
    {
      level: 99,
      levelCount: 50,
      progressWidth: 100,
    },
  ];
  const showZhuoRui = true;
  return (
    <div className={styles.preview}>
      <div className={styles['member-info']} style={{ backgroundImage: `url(${data.moduleBgImg})` }}>
        {/* 用户名、等级 */}
        <div className={styles['nick-level']}>
          <div className={styles['text-1']}>
            尊敬的
            <img style={{ width: '30px' }} src={LevelImageMap[0]} alt="" />
            会员名称
          </div>
        </div>
        <div className={styles['welcome-text']}>欢迎来到飞鹤之家</div>
        {/* 累计正装罐数、卓睿星币 */}
        <div className={styles['order-count-container']}>
          <div className={styles['order-count']}>
            <div className={styles['count-tips']}>
              <div className={styles['count-tips-text']}>累计正装金额</div>
              <img
                className={styles['count-tips-icon']}
                src="https://img10.360buyimg.com/imgzone/jfs/t1/233758/39/9314/4412/6589230fF481f51ee/d2290501459cfd43.png"
              />
            </div>
            <div className={styles['count-text']}>10</div>
          </div>
          <div className={styles['order-count']} style={{ display: 'none' }}>
            <div className={styles['count-tips']}>
              <div className={styles['count-tips-text']}>{allJson.showZhuoRui ? '卓睿星币' : '飞鹤星币'}</div>
              <img
                className={styles['count-tips-icon']}
                src="https://img10.360buyimg.com/imgzone/jfs/t1/233758/39/9314/4412/6589230fF481f51ee/d2290501459cfd43.png"
              />
            </div>
            <div className={styles['count-text']}>0</div>
          </div>
          {!allJson.showZhuoRui && (
            <div className={styles['change-zr-button']} style={{ display: 'none' }}>
              <div className={styles['zr-order-count']}>
                <div className={styles.text}>卓睿星币</div>
                <div className={styles.num}>00</div>
              </div>
            </div>
          )}
        </div>
        {/* 等级进度条 */}
        <div className={styles['member-level-progress-container']}>
          {/* 进度条背景 */}
          <div className={styles['member-level-progress-bg']}>
            {levelList.slice(0, 5).map((levelItem, levelIndex) => (
              <div
                key={levelIndex}
                className={`${styles['member-level-info']} ${styles[`member-level-info-${levelIndex}`]}`}
                style={{ left: `${levelItem.percent}%` }}
              >
                {/* 等级名称 */}
                <div
                  className={`${styles['member-level-icon']}`}
                  style={{ backgroundImage: `url(${levelItem.levelIcon})` }}
                />
                {/* 是否点亮小图标 */}
                <div
                  className={`${styles['member-level-star-icon']} ${
                    currentLevel >= levelItem.level ? `${styles['member-level-star-icon-active']}` : ``
                  }`}
                />
                {/* 等级罐数 */}
                <div className={styles['member-level-count']}>
                  {data.memberLevels ? data.memberLevels[levelIndex + 2]?.minCans : levelItem.levelCount}
                </div>
              </div>
            ))}
            {/* 进度条 */}
            <div className={styles['member-level-progress']} style={{ width: `10%` }} />
          </div>
        </div>
      </div>
    </div>
  );
};
