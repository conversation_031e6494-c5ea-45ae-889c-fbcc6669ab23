import React from 'react';
import styles from './index.module.scss';
import { saveZphyJson } from '@/api/zphy';
import { <PERSON>, Button, Divider, Message } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';

function MemberRule({ data, dispatch, allJson, defaultData }) {
  console.log('MemberRule', data, defaultData);
  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };

  const [pageLoading, setPageLoading] = React.useState(false);
  React.useEffect(() => {}, []);

  const saveSetting = (): any => {
    if (!data.img) {
      Message.error('请上传图片');
      return false;
    }

    let updateJson = {};
    if (allJson.showZhuoRui) {
      updateJson = {
        ...allJson,
        ZRNewMember: {
          ...allJson.ZRNewMember,
          MemberRule: { ...data },
        },
        ZROldMember: {
          ...allJson.ZROldMember,
          MemberRule: { ...data },
        },
      };
    } else {
      updateJson = {
        ...allJson,
        NewMember: {
          ...allJson.NewMember,
          MemberRule: { ...data },
        },
        OldMember: {
          ...allJson.OldMember,
          MemberRule: { ...data },
        },
      };
    }
    console.log('updateJson', updateJson);
    const params = {
      json: JSON.stringify(updateJson),
      type: 1,
      version: allJson.version,
    };
    setPageLoading(true);
    saveZphyJson(params)
      .then((res) => {
        Message.success('保存成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };

  const commonProps = {
    title: '会员规则',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <div className={styles.operation}>
            <div className={styles.kvContainer}>
              <div className="crm-label">会员规则</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  width={750}
                  height={[0, 2000]}
                  value={data.img}
                  onChange={(img) => {
                    setData({ img });
                  }}
                />
                <div className={styles.tip}>
                  <div>图片尺寸：仅支持宽度为750px，高度不超过2000px</div>
                  <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                </div>
              </div>
              <div className={styles.colorPicker}>
                <Button
                  style={{ marginLeft: '35px' }}
                  type={'primary'}
                  text
                  onClick={() => {
                    setData({ img: defaultData.MemberRule.img });
                  }}
                >
                  重置
                </Button>
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}

export default MemberRule;
