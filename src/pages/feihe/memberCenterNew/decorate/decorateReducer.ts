// 会员卡
import MemberCardDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/MemberCard';
import MemberCardPreview from '@/pages/feihe/memberCenterNew/decorate/modules/MemberCard/preview';

// // 会员专属福利
// import BiActivityDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/BiActivity';
// import BiActivityPreview from '@/pages/feihe/memberCenterNew/decorate/modules/BiActivity/preview';

// // 星币兑换
// import StarGoldExchangeDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/StarGoldExchange';
// import StarGoldExchangePreview from '@/pages/feihe/memberCenterNew/decorate/modules/StarGoldExchange/preview';

// // 非卓睿升级广告位
// import ChangeZrImgDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/ChangeZrImg';
// import ChangeZrImgPreview from '@/pages/feihe/memberCenterNew/decorate/modules/ChangeZrImg/preview';

// // 等级权益
// import ClassInterestDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/ClassInterest';
// import ClassInterestPreview from '@/pages/feihe/memberCenterNew/decorate/modules/ClassInterest/preview';

// // 成长地图
// import GrowMapListDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/GrowMapList';
// import GrowMapListPreview from '@/pages/feihe/memberCenterNew/decorate/modules/GrowMapList/preview';
// // 成长记录册
// import MemberBabyInfoDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/MemberBabyInfo';
// import MemberBabyInfoPreview from '@/pages/feihe/memberCenterNew/decorate/modules/MemberBabyInfo/preview';

// // 会员折上折
// import MemberDiscountDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/MemberDiscount';
// import MemberDiscountPreview from '@/pages/feihe/memberCenterNew/decorate/modules/MemberDiscount/preview';
// // 新客专享价
// import MemberNewGiftDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/MemberNewGift';
// import MemberNewGiftPreview from '@/pages/feihe/memberCenterNew/decorate/modules/MemberNewGift/preview';

// // 转段福利
// import MemberTransferBenefitsDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/MemberTransferBenefits';
// import MemberTransferBenefitsPreview from '@/pages/feihe/memberCenterNew/decorate/modules/MemberTransferBenefits/preview';
// 互动好礼
import MemberInteractiveGiftDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/MemberInteractiveGift';
import MemberInteractiveGiftPreview from '@/pages/feihe/memberCenterNew/decorate/modules/MemberInteractiveGift/preview';
// 会员权益
import MemberBenefitDescriptionDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/MemberBenefitDescription';
import MemberBenefitDescriptionPreview from '@/pages/feihe/memberCenterNew/decorate/modules/MemberBenefitDescription/preview';
// 会员规则
import MemberRuleDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/MemberRule';
import MemberRulePreview from '@/pages/feihe/memberCenterNew/decorate/modules/MemberRule/preview';

// 热区
// import HotZonePreview from '@/pages/feihe/memberCenterNew/decorate/modules/HotZone/preview';
// import HotZoneDecorate from '@/pages/feihe/memberCenterNew/decorate/modules/HotZone';

export const initialState = () => {
  return {
    selectedModule: 'MemberCard',
    previews: {
      MemberCard: {
        previewComponent: MemberCardPreview,
        decorateComponent: MemberCardDecorate,
        name: '会员卡',
      },
      // BiActivity: {
      //   previewComponent: BiActivityPreview,
      //   decorateComponent: BiActivityDecorate,
      //   name: '专属福利',
      // },
      // StarGoldExchange: {
      //   previewComponent: StarGoldExchangePreview,
      //   decorateComponent: StarGoldExchangeDecorate,
      //   name: '星币兑换',
      // },
      // classInterest: {
      //   previewComponent: ClassInterestPreview,
      //   decorateComponent: ClassInterestDecorate,
      //   name: '等级权益',
      // },
      // ChangeZrImg: {
      //   previewComponent: ChangeZrImgPreview,
      //   decorateComponent: ChangeZrImgDecorate,
      //   name: '升级卓睿',
      // },
      // GrowMapList: {
      //   previewComponent: GrowMapListPreview,
      //   decorateComponent: GrowMapListDecorate,
      //   name: '成长地图',
      // },
      // MemberBabyInfo: {
      //   previewComponent: MemberBabyInfoPreview,
      //   decorateComponent: MemberBabyInfoDecorate,
      //   name: '成长记录',
      // },
      // MemberDiscount: {
      //   previewComponent: MemberDiscountPreview,
      //   decorateComponent: MemberDiscountDecorate,
      //   name: '折上折',
      // },
      // MemberNewGift: {
      //   previewComponent: MemberNewGiftPreview,
      //   decorateComponent: MemberNewGiftDecorate,
      //   name: '新客专享价',
      // },
      // MemberTransferBenefits: {
      //   previewComponent: MemberTransferBenefitsPreview,
      //   decorateComponent: MemberTransferBenefitsDecorate,
      //   name: '转段福利',
      // },
      // MemberInteractiveGift: {
      //   previewComponent: MemberInteractiveGiftPreview,
      //   decorateComponent: MemberInteractiveGiftDecorate,
      //   name: '互动好礼',
      // },
      // MemberBenefitDescription: {
      //   previewComponent: MemberBenefitDescriptionPreview,
      //   decorateComponent: MemberBenefitDescriptionDecorate,
      //   name: '会员权益',
      // },
      // MemberRule: {
      //   previewComponent: MemberRulePreview,
      //   decorateComponent: MemberRuleDecorate,
      //   name: '会员规则',
      // },
      // hotZone: {
      //   previewComponent: HotZonePreview,
      //   decorateComponent: HotZoneDecorate,
      //   name: '备用互动热区',
      // },
    },
    modules: {
      MemberCard: {
        // kv图
        hotDataList: [],
        moduleBgImg: '',
        adDialog: {
          showAd: false, // 是否显示广告弹窗
          adImgUrl: '', // 广告弹窗图片
          adLink: '', // 广告弹窗点击跳转地址
        },
        memberLevels: [
          {
            level: 0,
            minCans: 0,
          },
          {
            level: 1,
            minCans: 0,
          },
          {
            level: 2,
            minCans: 1,
          },
          {
            level: 3,
            minCans: 1500,
          },
          {
            level: 4,
            minCans: 5000,
          },
          {
            level: 5,
            minCans: 10000,
          },
        ],
        currentLevel: 1,
        levelColor: '',
        nickColor: '',
        textColor: '',
        btnImg: '',
        btnLink: '',
      },
      BiActivity: {},
      StarGoldExchange: {},
      ChangeZrImg: {}, // 非卓睿升级广告位
      GrowMapList: {},
      MemberBabyInfo: {},
      MemberDiscount: {},
      MemberNewGift: {
        showZhuoRui: true, // 卓睿和非卓睿切换
      },
      MemberTransferBenefits: {},
      MemberInteractiveGift: {},
      MemberBenefitDescription: {},
      MemberRule: {},
      hotZone: [],
    },
    id: null,
    defaultValue: {}, // 重置用的原始数据
    activityActiveTab: {
      biTab: '1',
      newGiftTab: '1',
    }, // 会员专属福利切换人群tab、新客专享价切换按钮tab，预览需要展示不同样式
    allJson: {}, // 接口返回的完整json
    refreshPage: false,
  };
};
function updateModule(modules, selectedModule, payload) {
  if (Array.isArray(modules[selectedModule])) {
    return {
      ...modules,
      [selectedModule]: payload,
    };
  } else {
    return {
      ...modules,
      [selectedModule]: {
        ...modules[selectedModule],
        ...payload,
      },
    };
  }
}

export function decorateReducer(state, action) {
  switch (action.type) {
    case 'SELECT_MODULE':
      return { ...state, selectedModule: action.payload };
    case 'UPDATE_MODULE':
      return {
        ...state,
        modules: updateModule(state.modules, state.selectedModule, action.payload),
      };
    case 'INIT_MODULE': {
      return {
        ...state,
        modules: {
          ...state.modules,
          ...action.payload,
        },
      };
    }
    case 'INIT_DEFAULT': {
      return {
        ...state,
        defaultValue: {
          ...action.payload,
        },
      };
    }
    case 'UPDATE_REFRESHPAGE': {
      return {
        ...state,
        refreshPage: action.payload,
      };
    }
    case 'INIT_SUBMEMBERCARD': {
      return {
        ...state,
        modules: {
          ...state.modules,
          BiActivity: action.payload,
          ChangeZrImg: action.payload,
          GrowMapList: action.payload,
          classInterest: action.payload,
          StarGoldExchange: action.payload,
        },
      };
    }

    case 'UPDATE_ACTIVITYACTIVETAB': {
      return {
        ...state,
        activityActiveTab: action.payload,
      };
    }
    case 'INIT_SHOWZHUORUI': {
      return {
        ...state,
        modules: {
          ...state.modules,
          MemberNewGift: {
            ...state.modules.MemberNewGift,
            showZhuoRui: action.payload,
          },
        },
      };
    }
    case 'INIT_ALLJSON': {
      return {
        ...state,
        allJson: action.payload,
      };
    }
    default:
      return state;
  }
}
