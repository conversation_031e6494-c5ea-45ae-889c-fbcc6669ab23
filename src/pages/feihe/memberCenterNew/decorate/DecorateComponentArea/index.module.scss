.decorateComponent {
  width: 100%;
  height: calc(100vh - 155px);
  overflow-y: scroll;
  position: sticky;
  top: 20px;

  .noOrder {
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    img {
      width: 88px;
      height: 88px;
    }
    .title {
      font-weight: bold;
      font-size: 13px;
    }
    .desc {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.45);
      margin: 8px 0;
    }
    .orderBtn {
      display: flex;
      align-items: center;

      i {
        font-size: 12px;
        margin-left: 5px;
      }
    }
  }
}
