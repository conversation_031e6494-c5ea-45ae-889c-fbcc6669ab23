import React from 'react';
import styles from './index.module.scss';

const DecorateComponent = ({ state, dispatch, moduleName }) => {
  const Components = state.previews[moduleName].decorateComponent;
  return (
    <Components
      data={state.modules[moduleName]}
      allJson={state.allJson}
      dispatch={dispatch}
      defaultData={state.defaultValue}
      activityActiveTab={state.activityActiveTab}
    />
  );
};

function DecorateComponentArea({ selectedModule, state, dispatch }) {
  React.useEffect(() => {}, []);
  return (
    <div className={styles.decorateComponent}>
      <DecorateComponent state={state} dispatch={dispatch} moduleName={selectedModule} />
    </div>
  );
}

export default DecorateComponentArea;
