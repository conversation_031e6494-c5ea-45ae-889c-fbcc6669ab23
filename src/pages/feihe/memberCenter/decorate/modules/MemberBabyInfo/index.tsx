import { save<PERSON><PERSON><PERSON><PERSON> } from '@/api/zphy';
import LzImageSelector from '@/components/LzImageSelector';
import { urlRegularCheck } from '@/pages/feihe/memberCenter/decorate/utils';
import { Button, Card, Divider, Field, Form, Input, Message, Switch } from '@alifd/next';
import React from 'react';
import styles from './index.module.scss';

const formItemLayout = {
  labelCol: {
    span: 2,
  },
  wrapperCol: {
    span: 22,
  },
  labelAlign: 'left',
  colon: true,
};

function MemberBabyInfo({ data, dispatch, allJson, defaultData }) {
  console.log('MemberBabyInfo', data, defaultData);
  const [pageLoading, setPageLoading] = React.useState(false);
  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };
  const field = Field.useField();

  React.useEffect(() => {}, []);

  const saveSetting = (): any => {
    if (!data.serviceImg) {
      Message.error('请上传图片');
      return false;
    }
    if (!data.serviceLink) {
      Message.error('请输入链接');
      return false;
    }
    let updateJson = {};
    if (allJson.showZhuoRui) {
      updateJson = {
        ...allJson,
        ZRNewMember: {
          ...allJson.ZRNewMember,
          MemberBabyInfo: { ...data },
        },
        ZROldMember: {
          ...allJson.ZROldMember,
          MemberBabyInfo: { ...data },
        },
      };
    } else {
      updateJson = {
        ...allJson,
        NewMember: {
          ...allJson.NewMember,
          MemberBabyInfo: { ...data },
        },
        OldMember: {
          ...allJson.OldMember,
          MemberBabyInfo: { ...data },
        },
      };
    }
    console.log('updateJson', updateJson);
    const params = {
      json: JSON.stringify(updateJson),
      type: 1,
      version: allJson.version,
    };
    setPageLoading(true);
    saveZphyJson(params)
      .then((res) => {
        Message.success('保存成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };

  const commonProps = {
    title: '成长记录册',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <div className={styles.operation}>
            <div className={styles.kvContainer}>
              <div className="crm-label">转段礼设置</div>
              <Form field={field} {...formItemLayout}>
                <Form.Item name="serviceText" label="展示文案设置">
                  <Input
                    value={data.serviceText}
                    trim
                    placeholder="请输入展示文案"
                    maxLength={11}
                    showLimitHint
                    style={{ width: 380 }}
                    onChange={(serviceText) => {
                      setData({ serviceText });
                    }}
                  />
                </Form.Item>
              </Form>
              <div className={styles.imgUpload} style={{ marginBottom: 20 }}>
                <div>
                  <LzImageSelector
                    width={438}
                    height={284}
                    value={data.serviceImg}
                    onChange={(serviceImg) => {
                      setData({ serviceImg });
                    }}
                  />
                  <Button
                    style={{ marginLeft: '35px', marginTop: '5px' }}
                    type={'primary'}
                    text
                    onClick={() => {
                      setData({ serviceImg: defaultData.MemberBabyInfo.serviceImg });
                    }}
                  >
                    重置
                  </Button>
                </div>
                <div className={styles.tip}>
                  <div>图片尺寸：为438*284px,支持jpg、jpeg、png格式，大小不超过1M</div>
                  <div style={{ display: 'flex', alignItems: 'baseline', marginTop: '10px' }}>
                    <div style={{ width: '100px' }}>配置链接:</div>
                    <Form style={{ width: '100%' }} field={field}>
                      <Form.Item
                        required
                        name="serviceLink"
                        requiredMessage={'请输入配置链接'}
                        validator={urlRegularCheck}
                      >
                        <Input
                          style={{ width: '300px' }}
                          name="serviceLink"
                          value={data.serviceLink}
                          placeholder={'请输入配置链接'}
                          onFocus={() => {}}
                          onChange={(serviceLink) => {
                            setData({ serviceLink });
                          }}
                        />
                      </Form.Item>
                    </Form>
                  </div>
                </div>
              </div>

              <Form field={field} inline>
                <Form.Item label="是否展示联系客服领取按钮">
                  <Switch
                    checked={data.serviceChatImgShow}
                    onChange={(value) => {
                      setData({
                        serviceChatImgShow: value,
                      });
                    }}
                  />
                </Form.Item>
              </Form>
              <div className={styles.imgUpload}>
                <div>
                  <LzImageSelector
                    width={345}
                    height={90}
                    value={data.serviceChatImg}
                    onChange={(serviceChatImg) => {
                      setData({ serviceChatImg });
                    }}
                  />
                  <Button
                    style={{ marginLeft: '35px', marginTop: '5px' }}
                    type={'primary'}
                    text
                    onClick={() => {
                      setData({ serviceChatImg: defaultData.MemberBabyInfo.serviceChatImg });
                    }}
                  >
                    重置
                  </Button>
                </div>
                <div className={styles.tip}>
                  <div>图片尺寸：为345*90px,支持jpg、jpeg、png格式，大小不超过1M</div>
                </div>
              </div>
            </div>

            <div className={styles.kvContainer}>
              <div className="crm-label">生日礼配置</div>
              <Form field={field} {...formItemLayout}>
                <Form.Item name="birthGiftText" label="展示文案设置">
                  <Input
                    value={data.birthGiftText}
                    trim
                    placeholder="请输入展示文案"
                    maxLength={11}
                    showLimitHint
                    style={{ width: 380 }}
                    onChange={(birthGiftText) => {
                      setData({ birthGiftText });
                    }}
                  />
                </Form.Item>
              </Form>
              <div className={styles.imgUpload}>
                <div>
                  <LzImageSelector
                    width={180}
                    height={158}
                    value={data.birthGiftImg}
                    onChange={(birthGiftImg) => {
                      setData({ birthGiftImg });
                    }}
                  />
                  <Button
                    style={{ marginLeft: '35px', marginTop: '5px' }}
                    type={'primary'}
                    text
                    onClick={() => {
                      setData({ birthGiftImg: defaultData.MemberBabyInfo.birthGiftImg });
                    }}
                  >
                    重置
                  </Button>
                </div>
                <div className={styles.tip}>
                  <div>图片尺寸：为180*158px,支持jpg、jpeg、png格式，大小不超过1M</div>
                </div>
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}

export default MemberBabyInfo;
