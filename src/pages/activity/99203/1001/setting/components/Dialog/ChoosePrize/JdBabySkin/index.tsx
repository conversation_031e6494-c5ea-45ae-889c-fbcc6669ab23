import { Radio, Table, Form, Button, NumberPicker, Input, Dialog, Grid, Field, Message } from '@alifd/next';
import React, { useState, useReducer, useEffect } from 'react';
import Plan from './components/Plan';
import styles from './index.module.scss';
import { activityEditDisabled, numRegularCheckInt } from '@/utils';
import format from '@/utils/format';

interface ComponentProps {
  [propName: string]: any;
}

// eslint-disable-next-line complexity
const PropertyJdProduct = ({
  editValue,
  onChange,
  onCancel,
  hasProbability = true,
  hasLimit = true,
  hasOrderPrice = false,
  productImgTip = false,
  width,
  height,
  prizeNameLength,
  formData,
  sendTotalCountMax = 999999999,
}: ComponentProps) => {
  const defaultValue = {
    prizeKey: null,
    prizeType: 901,
    dayLimitType: 1,
    dayLimit: 1,
    unitCount: 1,
    prizeImg: '',
  };
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field = Field.useField();
  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  const submit = (): void => {
    console.log('0000', prizeData);
    if (prizeData.prizeName === '' || prizeData.prizeName === undefined) {
      Message.error(`请输入奖品名称!`);
      return;
    }
    if (prizeData.sendTotalCount === 0 || prizeData.sendTotalCount === undefined) {
      Message.error(`请输入发放总量!`);
      return;
    }
    onChange(prizeData);
  };
  const onSubmit = (resource: any) => {
    if (!resource) {
      return;
    }
    resource.prizeKey = resource.skinId;
    resource.prizeName = resource.skinName;
    resource.prizeImg = resource.skinImg;
    console.log('resource', resource);
    setPrizeData(resource);
    setPrizeListList([
      {
        prizeType: 901,
        prizeName: resource.skinName,
        prizeImg: resource.skinImg,
        prizeKey: resource.skinId,
      },
    ]);
    setPlanVisible(true);
    field.setErrors({ prizeKey: '', prizeName: '' });
    if (resource.prizeImg) field.setError('prizeImg', '');
  };
  const prizeFormLayout: any = {
    labelCol: {
      fixedSpan: 5,
    },
    colon: true,
  };
  const [planVisible, setPlanVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  // 当行点击重新选择的时候
  const reselect = (record: object) => {
    const data = { ...prizeData };
    data.prizeKey = null;
    data.prizeName = '';
    data.sendTotalCount = 0;
    setPrizeData(data);
    setPlanVisible(false);
  };
  const [prizeList, setPrizeListList] = useState<any[]>([
    {
      prizeType: 901,
      prizeName: '',
      prizeImg: '',
      prizeKey: '',
    },
  ]);
  // 取消选择 重置信息
  const cancle = () => {
    const data = { ...prizeData };
    data.prizeKey = null;
    data.prizeName = '';
    data.sendTotalCount = 0;
    setPrizeData(data);
    setPlanVisible(false);
  };
  // 目前存在老资产没有锁定库存的问题，暂时不获取可用库存
  React.useEffect(() => {
    if (editValue) {
      setPrizeListList([
        {
          prizeType: 901,
          prizeName: editValue.skinName,
          prizeImg: editValue.skinImg,
          prizeKey: editValue.skinId,
        },
      ]);
      setPlanVisible(true);
    }
  }, []);
  return (
    <div className={styles.PropertyJdProduct}>
      {!planVisible && <Plan onSubmit={onSubmit} />}
      {planVisible && (
        <>
          <div className={styles.planBg} />
          <Table.StickyLock
            dataSource={prizeList}
            primaryKey="skinId"
            fixedHeader
            maxBodyHeight={600}
            loading={loading}
          >
            <Table.Column align="left" title="皮肤名称" dataIndex="prizeName" />
            <Table.Column
              align="left"
              title="皮肤图片"
              dataIndex="prizeImg"
              cell={(value, index, data) => {
                return <>{<img style={{ width: '80px' }} src={value} alt=" " />}</>;
              }}
            />
            <Table.Column
              align="left"
              title="操作"
              cell={(value, index, records) => (
                <Button type="primary" text onClick={reselect.bind(this, records)}>
                  {'重新选择'}
                </Button>
              )}
            />
          </Table.StickyLock>
          <div style={{ marginTop: '10px' }}>
            <Form field={field} {...prizeFormLayout}>
              <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
                <Input
                  className={styles.formInputCtrl}
                  maxLength={prizeNameLength}
                  showLimitHint
                  trim
                  placeholder="请输入奖品名称"
                  name="prizeName"
                  value={prizeData.prizeName}
                  onChange={(prizeName) => setData({ prizeName })}
                />
                <span style={{ marginLeft: 10 }}>(用于活动主页展示)</span>
              </Form.Item>
              <Form.Item label="单位数量" validator={numRegularCheckInt}>
                <NumberPicker
                  className={styles.formNumberPicker}
                  placeholder="请输入单位数量"
                  type="inline"
                  min={1}
                  defaultValue={1}
                  disabled
                  step={1}
                  // value={prizeData.sendTotalCount}
                  // onChange={(sendTotalCount) => setData({ sendTotalCount })}
                />
              </Form.Item>
              <Form.Item label="发放总量" required requiredMessage="请输入发放总量" validator={numRegularCheckInt}>
                <NumberPicker
                  className={styles.formNumberPicker}
                  placeholder="请输入发放份数"
                  type="inline"
                  min={1}
                  max={sendTotalCountMax}
                  step={1}
                  name="sendTotalCount"
                  value={prizeData.sendTotalCount}
                  onChange={(sendTotalCount) => setData({ sendTotalCount })}
                />
              </Form.Item>
            </Form>
          </div>
          <div
            style={{
              width: '100%',
              height: '40px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              marginTop: '30px',
            }}
          >
            <Button key="confirm" size="large" type="primary" style={{ marginRight: '10px' }} onClick={() => submit()}>
              保存
            </Button>
            <Button key="cancel" size="large" onClick={() => cancle()}>
              取消
            </Button>
          </div>
          <div />
        </>
      )}
    </div>
  );
};

export default PropertyJdProduct;
