/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 基础元素
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzColorPicker from '@/components/LzColorPicker';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}
export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel title="活动背景">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="活动主图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  value={formData.actBg}
                  onChange={(actBg) => {
                    setForm({ actBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px、推荐图片高度851px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ actBg: defaultValue?.actBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          {/* <Form.Item label="页面背景颜色"> */}
          {/*  <LzColorPicker value={formData.actBgColor} onChange={(actBgColor) => setForm({ actBgColor })} /> */}
          {/*  <Button */}
          {/*    type="primary" */}
          {/*    text */}
          {/*    onClick={() => { */}
          {/*      setForm({ actBgColor: defaultValue?.actBgColor }); */}
          {/*    }} */}
          {/*  > */}
          {/*    重置 */}
          {/*  </Button> */}
          {/* </Form.Item> */}
        </Form>
      </LzPanel>
      {/* <LzPanel title="基础元素"> */}
      {/*  <Form {...formLayout} className={styles.form}> */}
      {/*    /!* <Form.Item label="文字颜色"> *!/ */}
      {/*    /!*  <LzColorPicker value={formData.shopNameColor} onChange={(shopNameColor) => setForm({ shopNameColor })} /> *!/ */}
      {/*    /!*  <Button *!/ */}
      {/*    /!*    type="primary" *!/ */}
      {/*    /!*    text *!/ */}
      {/*    /!*    onClick={() => { *!/ */}
      {/*    /!*      setForm({ shopNameColor: defaultValue.shopNameColor }); *!/ */}
      {/*    /!*    }} *!/ */}
      {/*    /!*  > *!/ */}
      {/*    /!*    重置 *!/ */}
      {/*    /!*  </Button> *!/ */}
      {/*    /!* </Form.Item> *!/ */}
      {/*    <Form.Item label="按钮"> */}
      {/*      <div className={styles.colorList}> */}
      {/*        <div> */}
      {/*          <span>边框</span> */}
      {/*          <LzColorPicker */}
      {/*            value={formData.btnBorderColor} */}
      {/*            onChange={(btnBorderColor) => setForm({ btnBorderColor })} */}
      {/*          /> */}
      {/*          <Button */}
      {/*            type="primary" */}
      {/*            text */}
      {/*            onClick={() => { */}
      {/*              setForm({ btnBorderColor: defaultValue?.btnBorderColor }); */}
      {/*            }} */}
      {/*          > */}
      {/*            重置 */}
      {/*          </Button> */}
      {/*        </div> */}
      {/*        <div> */}
      {/*          <span>背景</span> */}
      {/*          <LzColorPicker value={formData.btnBg} onChange={(btnBg) => setForm({ btnBg })} /> */}
      {/*          <Button */}
      {/*            type="primary" */}
      {/*            text */}
      {/*            onClick={() => { */}
      {/*              setForm({ btnBg: defaultValue?.btnBg }); */}
      {/*            }} */}
      {/*          > */}
      {/*            重置 */}
      {/*          </Button> */}
      {/*        </div> */}
      {/*        <div> */}
      {/*          <span>文字</span> */}
      {/*          <LzColorPicker value={formData.btnColor} onChange={(btnColor) => setForm({ btnColor })} /> */}
      {/*          <Button */}
      {/*            type="primary" */}
      {/*            text */}
      {/*            onClick={() => { */}
      {/*              setForm({ btnColor: defaultValue?.btnColor }); */}
      {/*            }} */}
      {/*          > */}
      {/*            重置 */}
      {/*          </Button> */}
      {/*        </div> */}
      {/*      </div> */}
      {/*    </Form.Item> */}
      {/*  </Form> */}
      {/* </LzPanel> */}
    </div>
  );
};
