/**
 * Author: liangyu
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer, useState } from 'react';
import { Form, Table, Input, Button } from '@alifd/next';
import { formItemLayout, GRADE_LABEL, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import ChoosePromotion from '@/components/ChoosePromotion';
import SkuDialog from '@/components/SkuDialog';
import { getMemberId } from '@/api/v90001';
import LzDialog from '@/components/LzDialog';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const [goodDialog, setGoodDialog] = useState(false);
  const [currentGood, setCurrentGood] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [memberList, setMemberList] = useState([]);

  const getMemberIds = async (activityKey) => {
    const res = await getMemberId({ activityKey });
    const data = res.map((item) => {
      return {
        memberId: item,
      };
    });
    setMemberList(data);
    setVisible(true);
  };

  formItemLayout.labelAlign = labelAlign;
  const showGoods = (good): void => {
    console.log(good, '商品展示');
    setCurrentGood(good);
    setGoodDialog(true);
  };

  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">店铺会员</FormItem>
        <FormItem label="赠品设置" isPreview={false} disabled>
          {/* eslint-disable-next-line complexity */}
          {formData.taskRequestList.map((items, index1) => {
            return (
              <div key={index1}>
                <div {...formItemLayout} key={index1}>
                  <div className={styles.awardTitle}>{GRADE_LABEL[index1].gradeName}会员</div>
                  <div>
                    <div className={styles.skuListDiv}>
                      <div className={styles.labelTitle}>奖品类型：</div>
                      <div style={{ marginLeft: '8px' }}> {items.prizeType === 11 ? '单品促销令牌' : '实物'}</div>
                    </div>
                  </div>
                  {(GRADE_LABEL[index1].gradeLevel === '1' || GRADE_LABEL[index1].gradeLevel === '2') && (
                    <div>
                      <div className={styles.skuListDiv}>
                        <div className={styles.labelTitle}>参与活动正装商品：</div>
                        {items.cpbJoinSkuType === 2 && (
                          <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                            <div style={{ color: '#1677ff', cursor: 'pointer' }} onClick={() => showGoods(items)}>
                              商品展示
                            </div>
                          </div>
                        )}
                        {items.cpbJoinSkuType === 1 && <div style={{ marginLeft: '8px' }}>全店正装商品</div>}
                      </div>
                    </div>
                  )}
                  {items.prizeType === 11 && (
                    <div>
                      <div className={styles.prizeListDiv11}>
                        <div className={styles.labelTitle}>添加令牌：</div>
                        <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                          {' '}
                          <ChoosePromotion
                            disabled
                            promoType={'1'}
                            index={index1}
                            value={formData.taskRequestList[index1].prizeList[0] ?? null}
                          />
                        </div>
                      </div>
                      <div className={styles.prizeListDiv11}>
                        <div className={styles.labelTitle}>令牌发放份数：</div>
                        <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                          {formData.taskRequestList[index1].prizeList[0].sendTotalCount}份
                        </div>
                      </div>
                    </div>
                  )}

                  {items.prizeType === 3 && (
                    <div>
                      <div className={styles.skuListDiv}>
                        <div className={styles.labelTitle}>奖品列表：</div>
                        <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                          <Table dataSource={items.prizeList} style={{ marginTop: '15px' }}>
                            <Table.Column
                              title="位置"
                              dataIndex="prizeName"
                              width={50}
                              cell={(_, index, row) => <div>{index + 1}</div>}
                            />
                            <Table.Column title="奖品名称" dataIndex="prizeName" />
                            <Table.Column
                              title="奖品类型"
                              cell={(_, index, row) => (
                                <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                                  {PRIZE_TYPE[row.prizeType]}
                                </div>
                              )}
                              dataIndex="prizeType"
                            />
                            <Table.Column
                              title="单位数量"
                              cell={(_, index, row) => <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>}
                            />
                            <Table.Column
                              title="发放份数"
                              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                            />
                            <Table.Column
                              title="单份价值(元)"
                              cell={(_, index, row) => (
                                <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>
                              )}
                            />
                            <Table.Column
                              title="奖品图"
                              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                            />
                          </Table>
                        </div>
                      </div>
                    </div>
                  )}

                  {items.prizeType === 11 && (
                    <div>
                      <div className={styles.skuListDiv}>
                        <div className={styles.labelTitle}>SKU ID：</div>
                        <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                          {' '}
                          <Input
                            disabled
                            placeholder="请输入令牌所绑定的单品促销活动的正装商品SKU ID，用来点击申领后跳转"
                            value={items.giftSkuList[0]}
                            name={`skuId${index1}`}
                            className="w-300"
                          />
                          <p className={styles.tip}>
                            1. 输入令牌所绑定的单品促销活动的正装商品SKU
                            ID，用于用户点击“即刻申请”领取令牌后，跳转对应的商详页
                          </p>
                          <p className={styles.tip}>
                            2.请务必填写当前会员等级对应的SKU ID，填写错将导致链接跳转错误，产生客诉
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                  {items.prizeType === 3 && (
                    <div>
                      <div className={styles.skuListDiv}>
                        <div className={styles.labelTitle}>发放时机：</div>
                        <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                          {' '}
                          {(GRADE_LABEL[index1].gradeLevel === '1' || GRADE_LABEL[index1].gradeLevel === '2') && (
                            <p className={styles.tip}>
                              用户符合生日礼参与门槛，并且当月在店铺成功下单付款正装产品订单且确认收货后，前往活动页面点击“即刻申请”按钮后，发放实物
                            </p>
                          )}
                          {(GRADE_LABEL[index1].gradeLevel === '3' ||
                            GRADE_LABEL[index1].gradeLevel === '4' ||
                            GRADE_LABEL[index1].gradeLevel === '5') && (
                            <p className={styles.tip}>
                              用户符合生日礼参与门槛，前往活动页面点击“即刻申请”按钮后，发放实物
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  {items.prizeType === 11 && (
                    <div>
                      <div className={styles.skuListDiv}>
                        <div className={styles.labelTitle}>发放时机：</div>
                        <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                          {' '}
                          {(GRADE_LABEL[index1].gradeLevel === '1' || GRADE_LABEL[index1].gradeLevel === '2') && (
                            <p className={styles.tip}>
                              用户符合生日礼参与门槛，并且当月在店铺成功下单付款正装产品订单且确认收货后，前往活动页面点击“即刻申请”按钮后，发放令牌
                            </p>
                          )}
                          {(GRADE_LABEL[index1].gradeLevel === '3' ||
                            GRADE_LABEL[index1].gradeLevel === '4' ||
                            GRADE_LABEL[index1].gradeLevel === '5') && (
                            <p className={styles.tip}>
                              用户符合生日礼参与门槛，前往活动页面点击“即刻申请”按钮后，发放令牌
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  <div className={styles.prizeListDiv11}>
                    <div className={styles.labelTitle}>memberId：</div>
                    <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                      <Button
                        onClick={() => {
                          getMemberIds(items.activityKey);
                        }}
                      >
                        查看memberId
                      </Button>
                    </div>
                  </div>
                  <div>
                    <div className={styles.skuListDiv}>
                      <div className={styles.labelTitle}>活动规则：</div>
                      <div className={styles.labelContent} style={{ marginLeft: '8px' }}>
                        <FormItem>
                          <Input.TextArea className={styles.textAreaStyle} disabled value={items.taskRule} />
                        </FormItem>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
      </Form>
      <SkuDialog visible={goodDialog} onClose={() => setGoodDialog(false)} skuList={currentGood.skuList} />

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <Table dataSource={memberList} style={{ marginTop: 20 }}>
          <Table.Column title="memberId" dataIndex="memberId" />
        </Table>
      </LzDialog>

      {/* <Dialog */}
      {/*  width={822} */}
      {/*  v2 */}
      {/*  title="查看商品" */}
      {/*  visible={goodDialog} */}
      {/*  footer={false} */}
      {/*  onClose={() => setGoodDialog(false)} */}
      {/*  onOk={() => setGoodDialog(false)} */}
      {/* > */}
      {/*  <div className={styles.container}> */}
      {/*    {currentGood.skuList?.map((sku, index11) => { */}
      {/*      return ( */}
      {/*        <div key={index11} className={styles.skuContainer}> */}
      {/*          <img className={styles.skuImg} src={sku.skuMainPicture} alt="" /> */}
      {/*          <div> */}
      {/*            <div className={styles.skuName}>{sku.skuName}</div> */}
      {/*            <div className={styles.skuId}>SKUID:{sku.skuId}</div> */}
      {/*            <div className={styles.price}>¥ {sku.jdPrice}</div> */}
      {/*          </div> */}
      {/*        </div> */}
      {/*      ); */}
      {/*    })} */}
      {/*  </div> */}
      {/* </Dialog> */}
    </div>
  );
};
