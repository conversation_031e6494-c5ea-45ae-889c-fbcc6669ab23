import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import { Form, Field, Radio, Input, Grid, Table } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData } from '../../../util';
import ChooseGoods from '@/components/ChooseGoods';
import styles from '../../style.module.scss';
import SkuList from '@/components/SkuList';
import LzImg from '@/components/LzImg';
import CONST from '@/utils/constant';
import LzImageSelector from '@/components/LzImageSelector';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const field: Field = Field.useField();

  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const handleSkuChange = (data) => {
    setData({ skuList: data });
    field.setErrors({ skuList: '' });
  };
  const removeSku = (index: number) => (): void => {
    formData.skuList.splice(index, 1);
    setData({ skuList: formData.skuList });
  };
  const handlePreview = (data) => {
    setData({ skuListPreview: data });
  };
  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  return (
    <div>
      <LzPanel title="曝光商品">
        <Form {...formItemLayout} field={field}>
          <FormItem label="是否添加曝光商品" required>
            <RadioGroup
              value={formData.isExposure}
              onChange={(isExposure: number) => {
                if (!isExposure) {
                  formData.skuList = [];
                  formData.skuListPreview = [];
                }
                setData({ isExposure, skuList: formData.skuList });
              }}
            >
              <Radio id="1" value={1}>
                是
              </Radio>
              <Radio id="0" value={0}>
                否
              </Radio>
            </RadioGroup>
            <Grid.Row>
              {formData.isExposure === 1 && (
                <FormItem style={{ marginTop: '15px' }}>
                  <FormItem required requiredMessage={'请选择曝光商品'}>
                    <ChooseGoods max={10} value={formData.skuList} onChange={handleSkuChange} />
                    <Input className="validateInput" name="skuList" value={formData.skuList} />
                  </FormItem>
                  {formData.skuList.length !== 0 && (
                    // <SkuList skuList={formData.skuList} handlePreview={handlePreview} removeSku={removeSku} />
                    <Table.StickyLock
                      primaryKey="skuId"
                      fixedHeader
                      maxBodyHeight={420}
                      dataSource={formData.skuList}
                      style={{ marginTop: '15px' }}
                    >
                      <Table.Column
                        title="商品信息"
                        cell={(val, index, record) => (
                          <div className={styles.part1} style={{ alignItems: 'center' }}>
                            {record.skuMainPicture?.indexOf('360buyimg.com') > -1 ? (
                              <LzImg
                                style={{ width: 60, height: 60, marginRight: '5px' }}
                                src={record.skuMainPicture}
                              />
                            ) : (
                              <LzImg
                                style={{ width: 60, height: 60, marginRight: '5px' }}
                                src={`${CONST.IMAGE_PREFIX}${record.skuMainPicture}`}
                              />
                            )}
                            <div>
                              <div className={styles.part1_p1}>{record.skuName}</div>
                              <div className={styles.part1_p2}>SKU编码：{record.skuId}</div>
                            </div>
                          </div>
                        )}
                      />
                      <Table.Column
                        title="京东价（元）"
                        width={120}
                        cell={(val, index, info) => <span>{new Intl.NumberFormat().format(info.jdPrice)}</span>}
                      />
                      <Table.Column
                        title="展示图"
                        width={132}
                        cell={(val, index, info) => (
                          <Form.Item
                            disabled={false}
                            name={`skuImg${info.skuId}`}
                            required
                            requiredMessage="请上传展示图"
                            style={{ marginBottom: 0 }}
                          >
                            <LzImageSelector
                              value={info?.showSkuImage ?? ''}
                              onChange={(showSkuImage) => {
                                const skuList = [...formData.skuList];
                                skuList[index].showSkuImage = showSkuImage;
                                setData({ skuList });
                              }}
                            />
                            <Input
                              className="validateInput"
                              name={`skuImg${info.skuId}`}
                              value={info?.showSkuImage ?? ''}
                            />
                          </Form.Item>
                        )}
                      />
                    </Table.StickyLock>
                  )}
                  <p className={styles.tip}>
                    注：商品价格每天凌晨同步;
                    <br />
                    您在配置sku时，请注意其顺序，请务必与您在上一步中上传的商品图中的商品顺序保持一致；
                  </p>
                </FormItem>
              )}
            </Grid.Row>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
