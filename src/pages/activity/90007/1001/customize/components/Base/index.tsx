/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 基础元素
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzColorPicker from '@/components/LzColorPicker';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel title="活动背景">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="活动主图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  value={formData.actBg}
                  onChange={(actBg) => {
                    setForm({ actBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px、推荐图片高度939px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ actBg: defaultValue?.actBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="页面背景颜色">
            <LzColorPicker value={formData.actBgColor} onChange={(actBgColor) => setForm({ actBgColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ actBgColor: defaultValue?.actBgColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          {/* <Form.Item label="页面背景图"> */}
          {/*  <Grid.Row> */}
          {/*    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*      <LzImageSelector */}
          {/*        width={750} */}
          {/*        value={formData.pageBg} */}
          {/*        onChange={(pageBg) => { */}
          {/*          setForm({ pageBg }); */}
          {/*        }} */}
          {/*      /> */}
          {/*    </Form.Item> */}
          {/*    <Form.Item style={{ marginBottom: 0 }}> */}
          {/*      <div className={styles.tip}> */}
          {/*        <p>图片尺寸：宽度750px</p> */}
          {/*        <p>图片大小：不超过1M</p> */}
          {/*        <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*      </div> */}
          {/*      <div> */}
          {/*        <Button */}
          {/*          type="primary" */}
          {/*          text */}
          {/*          onClick={() => { */}
          {/*            setForm({ pageBg: defaultValue?.pageBg }); */}
          {/*          }} */}
          {/*        > */}
          {/*          重置 */}
          {/*        </Button> */}
          {/*      </div> */}
          {/*    </Form.Item> */}
          {/*  </Grid.Row> */}
          {/* </Form.Item> */}
        </Form>
      </LzPanel>
      <LzPanel title="基础元素">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="店铺名颜色">
            <LzColorPicker value={formData.shopNameColor} onChange={(shopNameColor) => setForm({ shopNameColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ shopNameColor: defaultValue.shopNameColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="按钮">
            <div className={styles.colorList}>
              <div>
                <span>边框</span>
                <LzColorPicker
                  value={formData.btnBorderColor}
                  onChange={(btnBorderColor) => setForm({ btnBorderColor })}
                />
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setForm({ btnBorderColor: defaultValue?.btnBorderColor });
                  }}
                >
                  重置
                </Button>
              </div>
              <div>
                <span>背景</span>
                <LzColorPicker value={formData.btnBg} onChange={(btnBg) => setForm({ btnBg })} />
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setForm({ btnBg: defaultValue?.btnBg });
                  }}
                >
                  重置
                </Button>
              </div>
              <div>
                <span>文字</span>
                <LzColorPicker value={formData.btnColor} onChange={(btnColor) => setForm({ btnColor })} />
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setForm({ btnColor: defaultValue?.btnColor });
                  }}
                >
                  重置
                </Button>
              </div>
            </div>
          </Form.Item>
          <Form.Item label="活动介绍">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={690}
                  height={672}
                  value={formData.acIntroductionBg}
                  onChange={(acIntroductionBg) => {
                    setForm({ acIntroductionBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度690px、高度672px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ acIntroductionBg: defaultValue?.acIntroductionBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="系列头部">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={690}
                  height={200}
                  value={formData.seriesBoxBkHead}
                  onChange={(seriesBoxBkHead) => {
                    setForm({ seriesBoxBkHead });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度690px、高度200px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ seriesBoxBkHead: defaultValue?.seriesBoxBkHead });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="系列中部">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={690}
                  // height={672}
                  value={formData.seriesBoxBkBody}
                  onChange={(seriesBoxBkBody) => {
                    setForm({ seriesBoxBkBody });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度690px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ seriesBoxBkBody: defaultValue?.seriesBoxBkBody });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="系列底部">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={690}
                  height={50}
                  value={formData.seriesBoxBkFooter}
                  onChange={(seriesBoxBkFooter) => {
                    setForm({ seriesBoxBkFooter });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度690px、高度50px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ seriesBoxBkFooter: defaultValue?.seriesBoxBkFooter });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="继续集罐按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={295}
                  height={67}
                  value={formData.buyBtnBg}
                  onChange={(buyBtnBg) => {
                    setForm({ buyBtnBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度295px、高度67px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ buyBtnBg: defaultValue?.buyBtnBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="兑换按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={205}
                  height={47}
                  value={formData.exchangeBtn}
                  onChange={(exchangeBtn) => {
                    setForm({ exchangeBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度205px、高度47px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ exchangeBtn: defaultValue?.exchangeBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="曝光商品顶部图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={282}
                  height={40}
                  value={formData.winnersBg}
                  onChange={(winnersBg) => {
                    setForm({ winnersBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度282px、高度40px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ winnersBg: defaultValue?.winnersBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
