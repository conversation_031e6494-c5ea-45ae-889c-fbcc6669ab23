/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2024-4-8 9:20
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import LzPanel from '@/components/LzPanel';
import { PageData, SeriesList } from '@/pages/activity/90007/2002/util';
import { Button, Dialog, Message, Upload } from '@alifd/next';
// 奖品信息
import PrizesInfo from '../Prizes';
import { deepCopy, downloadExcel, activityEditDisabled } from '@/utils';
import { seriesTemplateExport } from '@/api/v90007';
import { config } from 'ice';
import CONST from '@/utils/constant';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [fileList, setFileList] = useState<File[]>([]);
  const [uploaderRef, setUploaderRef] = useState(false);
  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    setUploaderRef(ref.getInstance());
  };

  const downloadTemplate = async () => {
    try {
      const data: any = await seriesTemplateExport();
      downloadExcel(data, '批量导入模板');
    } catch (error) {
      Message.error(error.message);
    }
  };

  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  const prizesRef: any[] = [];
  useImperativeHandle(sRef, (): { submit: () => object | null } => ({
    submit: () => {
      console.log(1213213);
      console.log(prizesRef);
      let err: object | null = null;
      for (let i = 0; i < prizesRef.length; i++) {
        const e = prizesRef[i].submit();
        if (e) {
          err = e;
        }
      }
      return err;
    },
  }));
  // <PrizesInfo sRef={prizesRef} {...settingProps} />
  return (
    <LzPanel>
      <div>
        <Message type="notice" style={{ marginBottom: 10 }}>
          导入须知： <br />
          1.导入前请下载模板，将你要上传的数据放入模板中，使用模板进行导入。
          <br />
          2.单次导入最大5M，导入中请不要关闭此页面。
          <br />
          <Button text type="primary" onClick={downloadTemplate}>
            下载模板
          </Button>
        </Message>
        <Upload
          action={`${config.baseURL}/90007/importSeriesExcel`}
          name="file"
          method="post"
          headers={{
            token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
            prd: localStorage.getItem(CONST.LZ_SSO_PRD),
          }}
          ref={saveUploaderRef}
          value={fileList}
          limit={1}
          listType="text"
          accept=".xls,.xlsx"
          onChange={(info) => {
            if (info.length) {
              if (info[0].size > 5 * 1024 * 1024) {
                Message.error('文件大小不能超过5M');
                return;
              }
            }
            prizesRef.splice(0);
            setFileList(info);
          }}
          onError={(res) => {
            if (res.state === 'error') {
              if (res.response?.message) {
                Message.error(res.response?.message);
              } else {
                Message.error('文件错误，请上传正确的文件');
              }
            }
          }}
          onSuccess={(res) => {
            console.log(res);
            if (res.response.code === 200) {
              console.log(res, '上传数据');
              // setTemporarySeriesList(res.response.data);
              const seriesList: SeriesList[] = [];
              res.response.data.forEach((item) => {
                seriesList.push({
                  seriesName: item.seriesName,
                  seriesPic: '',
                  seriesPrizeList: [],
                  seriesSkuList: item.seriesSkuInfoList,
                  seriesUrl: '',
                });
              });
              setData({ seriesList });
              Dialog.success({
                title: '导入结果',
                content: (
                  <div>
                    <p>导入成功</p>
                  </div>
                ),
                onOk: () => {
                  console.log('导入成功');
                },
              });
            } else if (res.response?.message) {
              setFileList([]);
              Message.error(res.response?.message);
            } else {
              setFileList([]);
              Message.error('文件错误，请上传正确的文件');
            }
          }}
          style={{ marginBottom: 10 }}
        >
          <div className="next-upload-drag">
            <p className="next-upload-drag-icon">
              <Button type="primary" disabled={activityEditDisabled()}>
                上传系列数据
              </Button>
            </p>
            <p className="next-upload-drag-hint">支持xls类型的文件</p>
          </div>
        </Upload>
      </div>
      {formData.seriesList &&
        formData.seriesList.map((item, index) => (
          <PrizesInfo
            sRef={(ref) => {
              prizesRef[index] = ref;
            }}
            value={item}
            onChange={(val) => {
              const seriesList = deepCopy(formData.seriesList);
              seriesList[index] = val;
              setData({ seriesList });
            }}
          />
        ))}
    </LzPanel>
  );
};
