import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form, Grid, Input, NumberPicker, Radio, Icon, Balloon } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, getShopOrderStartTime, isPopShop, validateActivityThreshold } from '@/utils';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import styles from '../../style.module.scss';
import ChooseGoods from '@/components/ChooseGoods';
import SkuList from '@/components/SkuList';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const ORDER_STATUS = [
  { label: '已付款', value: 0 },
  { label: '已完成', value: 1 },
];

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const [shopOrderStartTime, setShopOrderStartTime] = React.useState(dayjs().subtract(180, 'days').valueOf());
  // useEffect(() => {
  //   getShopOrderStartTime().then((res) => {
  //     setShopOrderStartTime(res);
  //   });
  // }, []);
  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: 0,
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
    });
    setTimeout(() => {
      field.validate('orderRestrainRangeData');
    }, 1000);
  }, [formData.endTime]);
  const [maxDays, setMaxDays] = React.useState(180);
  const [firstIn, setFirstIn] = React.useState(false);
  useEffect(() => {
    if (!firstIn) {
      setFirstIn(true);
      return;
    }
    let diff = 180;
    if (shopOrderInfo.longTermOrder) {
      diff = dayjs(formData.endTime).diff(dayjs(shopOrderInfo.shopOrderStartTime), 'day');
    } else {
      diff = shopOrderInfo.orderRetentionDays;
    }
    setMaxDays(diff);
    if (diff < formData.days) {
      setData({ days: diff });
    }
  }, [formData.startTime, shopOrderInfo.shopOrderStartTime]);

  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRestrainRangeData): void => {
    setData({
      orderRestrainRangeData,
      orderRestrainStartTime: format.formatDateTimeDayjs(orderRestrainRangeData[0]),
      orderRestrainEndTime: format.formatDateTimeDayjs(orderRestrainRangeData[1]),
    });
  };

  // 下单时间校验
  const validateOrderTime = (rule, val, callback): void => {
    // const { orderRestrainStartTime, orderRestrainEndTime } = formData;
    const orderRestrainStartTime = val[0];
    const orderRestrainEndTime = val[1];
    if (!orderRestrainStartTime || !orderRestrainEndTime) {
      callback('请选下单时间');
    } else if (
      !shopOrderInfo.longTermOrder &&
      dayjs(orderRestrainStartTime).valueOf() < shopOrderInfo.shopOrderStartTime
    ) {
      callback(`下单时间不能早于用户参与活动时间前${shopOrderInfo.orderRetentionDays}天`);
    } else if (dayjs(orderRestrainEndTime).startOf('s').isAfter(dayjs(formData.endTime))) {
      callback('下单时间不能晚于活动结束时间');
    } else {
      callback();
    }
  };

  const handleStatusChange = (orderRestrainStatus) => {
    setData({ orderRestrainStatus });
    if (orderRestrainStatus === 0 && formData.isDelayedDisttribution === 1) {
      setData({ orderRestrainStatus: 0, isDelayedDisttribution: 0, awardDays: 0 });
    }
  };

  const radioDisabled = () => {
    if (formData.orderRestrainStatus === 0) {
      return true;
    } else {
      return false;
    }
  };

  const handlePreview = (data) => {
    setData({ orderSkuListPreview: data });
  };

  const handleSkuChange = (data) => {
    setData({ orderSkuList: data });
    field.setErrors({ orderSkuList: '' });
  };
  const removeSku = (index: number) => (): void => {
    formData.orderSkuList.splice(index, 1);
    setData({ orderSkuList: formData.orderSkuList });
  };

  useEffect(() => {
    if (formData.orderStrokeCount === 2) {
      setData({ orderStrokeStatus: 2 });
    }
    if (formData.orderStrokeCount === 1) {
      setData({ orderStrokeStatus: 1 });
    }
  }, [formData.orderStrokeCount]);

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  const MoveTarget = <Icon type="help" id="top" style={{ marginRight: '10px' }} />;
  const rule = {
    1:
      '会员新单参与规则：\n' +
      '1，只统计【用户成为会员入会以后】的订单；\n' +
      '2，会员人群：默认选定下单之前的半年时间 - 没有购买记录的用户（具体未购时长配置可在【新单规则】手动配置）；',
    2:
      '店铺新单参与规则：\n' +
      '1，只统计【用户在店铺是否有过已完成的订单】，跟订单是否是会员身份购买无关；\n' +
      '2，新单人群：默认选定下单之前的半年时间 - 没有购买记录的用户（具体未购时长配置可在【新单规则】手动配置）',
  };
  const numbers =
    '1）如设置[单笔] (任意一笔订单) 则用户在满足订单规则前提下完成多笔订单，如任意一笔订单全额满足订单金额，则有领奖资格:\n' +
    '2）如设置[多笔]订单，则用户的[符合条件订单数] 满足最小订单条件，即可领奖';

  return (
    <div>
      <LzPanel title="订单限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          {/* <FormItem label="订单限制" disabled={false}> */}
          {/*  <Radio.Group */}
          {/*    value={formData.limitOrder} */}
          {/*    onChange={(limitOrder) => { */}
          {/*      setData({ limitOrder }); */}
          {/*    }} */}
          {/*  > */}
          {/*    /!* <Radio value={0}>不限制</Radio> *!/ */}
          {/*    <Radio value={1}>限制</Radio> */}
          {/*  </Radio.Group> */}
          {/* </FormItem> */}
          {/* <FormItem label="新单类型"> */}
          {/*  <Radio.Group */}
          {/*    value={formData.firstBuyType} */}
          {/*    onChange={(firstBuyType) => { */}
          {/*      setData({ firstBuyType }); */}
          {/*    }} */}
          {/*  > */}
          {/*    <Radio value={0}>会员新单</Radio> */}
          {/*    <Balloon v2 align="t" trigger={MoveTarget} triggerType="hover"> */}
          {/*      {rule['1']} */}
          {/*    </Balloon> */}
          {/*    <Radio value={1}>店铺新单</Radio> */}
          {/*    <Balloon v2 align="t" trigger={MoveTarget} triggerType="hover"> */}
          {/*      {rule['2']} */}
          {/*    </Balloon> */}
          {/*  </Radio.Group> */}
          {/* </FormItem> */}
          {!!formData.limitOrder && (
            <>
              <FormItem label="下单时间" required requiredMessage="请选下单时间" validator={validateOrderTime}>
                <RangePicker
                  className="w-300"
                  name="orderRestrainRangeData"
                  inputReadOnly
                  format={dateFormat}
                  hasClear={false}
                  showTime
                  value={
                    formData.orderRestrainRangeData || [
                      new Date(formData.orderRestrainStartTime),
                      new Date(formData.orderRestrainEndTime),
                    ]
                  }
                  onChange={onDataRangeChange}
                  disabledDate={(date) => {
                    return date.valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).subtract(0, 'day').valueOf();
                  }}
                />
                <div className={styles.tip}>
                  注：1、默认支持查询
                  {shopOrderInfo.longTermOrder
                    ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后`
                    : `用户参与活动时间前${shopOrderInfo.orderRetentionDays}天内`}
                  的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理。
                  <br />
                  2、若希望预售消费者参与本次活动，请在设置活动下单时间时包含预售开始时间（注：预售消费者支付定金的时间即为下单时间）。
                </div>
              </FormItem>
              <FormItem
                label="跨店拉订单店铺id"
                required
                requiredMessage="请输入需跨店拉订单的店铺Id"
              >
                <Input.TextArea
                  value={formData.orderShop}
                  placeholder="请输入店铺ID，多个用英文逗号分隔"
                  onChange={(value) => setData({ orderShop: value })}
                  style={{ width: '400px' }}
                />
                <div className={styles.tip}>
                  注：请输入需跨店拉订单的店铺Id，多个店铺Id用英文逗号分隔
                </div>
              </FormItem>
              {/* <FormItem */}
              {/*  label="订单状态" */}
              {/*  extra={ */}
              {/*    <div className="next-form-item-help"> */}
              {/*      已付款：用户付款后即可参与活动 */}
              {/*      <br /> */}
              {/*      已完成：(1)用户订单完成后才可参与活动。(2)预售商品需要支付尾款方可参与活动 */}
              {/*    </div> */}
              {/*  } */}
              {/* > */}
              {/*  <Select dataSource={ORDER_STATUS} value={formData.orderRestrainStatus} onChange={handleStatusChange} /> */}
              {/*  <div className={styles.orderTypes}> */}
              {/*    <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}> */}
              {/*      已付款 */}
              {/*    </span> */}
              {/*    <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}> */}
              {/*      待出库 */}
              {/*    </span> */}
              {/*    <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}> */}
              {/*      待发货 */}
              {/*    </span> */}
              {/*    <span className={styles.order}>已完成</span> */}
              {/*  </div> */}
              {/* </FormItem> */}
              <FormItem label="订单笔数" required>
                <FormItem>
                  <Radio.Group
                    value={formData.orderStrokeCount}
                    onChange={(orderStrokeCount) => setData({ orderStrokeCount })}
                  >
                    <Radio value={1}>单笔</Radio>
                    <Radio value={2}>多笔</Radio>
                    <Balloon v2 align="t" trigger={MoveTarget} triggerType="hover">
                      {numbers}
                    </Balloon>
                  </Radio.Group>
                </FormItem>
                {formData.orderStrokeCount === 2 && (
                  <FormItem required requiredMessage="请输入订单笔数">
                    大于等于{' '}
                    <NumberPicker
                      name="orderStrokeStatus"
                      min={1}
                      max={5}
                      type="inline"
                      value={formData.orderStrokeStatus}
                      onChange={(orderStrokeStatus: number) => setData({ orderStrokeStatus })}
                    />{' '}
                    笔
                  </FormItem>
                )}
              </FormItem>
              {/* <FormItem label="奖品延迟发放" required> */}
              {/*  <FormItem> */}
              {/*    <Radio.Group */}
              {/*      value={formData.isDelayedDisttribution} */}
              {/*      onChange={(isDelayedDisttribution) => */}
              {/*        setData({ isDelayedDisttribution, awardDays: isDelayedDisttribution }) */}
              {/*      } */}
              {/*    > */}
              {/*      <Radio value={0}>否</Radio> */}
              {/*      <Radio value={1} disabled={radioDisabled()}> */}
              {/*        是 */}
              {/*      </Radio> */}
              {/*    </Radio.Group> */}
              {/*  </FormItem> */}
              {/*  {formData.isDelayedDisttribution === 1 && formData.orderRestrainStatus === 1 && ( */}
              {/*    <FormItem required requiredMessage="请输入延迟天数"> */}
              {/*      延迟发放{' '} */}
              {/*      <NumberPicker */}
              {/*        name="awardDays" */}
              {/*        min={1} */}
              {/*        max={99} */}
              {/*        type="inline" */}
              {/*        value={formData.awardDays} */}
              {/*        onChange={(awardDays: number) => setData({ awardDays })} */}
              {/*      />{' '} */}
              {/*      天 */}
              {/*    </FormItem> */}
              {/*  )} */}
              {/* </FormItem> */}
              {/* <FormItem label="价格类型"> */}
              {/*  <Radio checked>京东价</Radio> */}
              {/* </FormItem> */}
              <FormItem label="价格类型">
                <Radio.Group value={formData.priceType} onChange={(val) => setData({ priceType: val })}>
                  <Radio value={0}>京东价</Radio>
                  <Radio value={1} disabled={!isPopShop()}>
                    实付价
                  </Radio>
                </Radio.Group>
              </FormItem>
              <FormItem label="订单限制类型" required>
                <Radio.Group
                  value={formData.itemTotalOrAmount}
                  onChange={(itemTotalOrAmount) => setData({ itemTotalOrAmount })}
                >
                  <Radio value={1}>件数</Radio>
                  {/* <Radio value={2}>金额</Radio> */}
                </Radio.Group>
              </FormItem>
              {formData.itemTotalOrAmount === 1 && (
                <FormItem label="订单件数" required requiredMessage="请输入件数">
                  大于等于{' '}
                  <NumberPicker
                    name="itemTotal"
                    min={1}
                    max={50}
                    type="inline"
                    value={formData.itemTotal}
                    onChange={(itemTotal: number) => setData({ itemTotal })}
                  />{' '}
                  件
                </FormItem>
              )}
              {formData.itemTotalOrAmount === 2 && (
                <FormItem
                  label={
                    formData.orderSkuisExposure === 1 || formData.orderSkuisExposure === 2
                      ? '指定商品总订单金额'
                      : '总订单金额'
                  }
                  required
                  requiredMessage="请输入订单金额"
                >
                  大于等于{' '}
                  <NumberPicker
                    precision={2}
                    name="orderRestrainAmount"
                    min={0}
                    max={9999999}
                    type="inline"
                    value={formData.orderRestrainAmount}
                    onChange={(orderRestrainAmount: number) => setData({ orderRestrainAmount })}
                  />{' '}
                  元
                </FormItem>
              )}
              <FormItem label="复购规则" required requiredMessage="请输入复购规则">
                近{' '}
                <NumberPicker
                  precision={2}
                  name="days"
                  min={0}
                  max={maxDays}
                  type="inline"
                  value={formData.days}
                  onChange={(days: number) => setData({ days })}
                />{' '}
                天已购
              </FormItem>
              <FormItem label="订单商品" required>
                <RadioGroup
                  value={formData.orderSkuisExposure}
                  onChange={(orderSkuisExposure: number) => {
                    formData.orderSkuList = [];
                    formData.orderSkuListPreview = [];
                    setData({ orderSkuisExposure, orderSkuList: formData.orderSkuList });
                  }}
                >
                  <Radio id="0" value={0}>
                    全部商品
                  </Radio>
                  <Radio id="1" value={1}>
                    指定商品
                  </Radio>
                  <Radio id="2" value={2}>
                    排除商品
                  </Radio>
                </RadioGroup>
                <Grid.Row>
                  {formData.orderSkuisExposure === 1 && (
                    <FormItem
                      name="orderSkuList"
                      required
                      requiredMessage={'请选择指定商品'}
                      style={{ marginTop: '15px' }}
                    >
                      <ChooseGoods value={formData.orderSkuList} onChange={handleSkuChange} />
                      <SkuList skuList={formData.orderSkuList} handlePreview={handlePreview} removeSku={removeSku} />

                      <p className={styles.tip}>注：选择指定商品后，活动也展示被添加商品，上限展示500个</p>
                      <Input className="validateInput" name="orderSkuList" value={formData.orderSkuList} />
                    </FormItem>
                  )}
                  {formData.orderSkuisExposure === 2 && (
                    <FormItem
                      name="orderSkuList"
                      required
                      requiredMessage={'请选择排除商品'}
                      style={{ marginTop: '15px' }}
                    >
                      <ChooseGoods value={formData.orderSkuList} onChange={handleSkuChange} />
                      <SkuList skuList={formData.orderSkuList} handlePreview={handlePreview} removeSku={removeSku} />

                      {/* <p className={styles.tip}>注：选择指定商品后，活动也展示被添加商品，上限展示500个</p> */}
                      <Input className="validateInput" name="orderSkuList" value={formData.orderSkuList} />
                    </FormItem>
                  )}
                </Grid.Row>
              </FormItem>
            </>
          )}
        </Form>
      </LzPanel>
    </div>
  );
};
