import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, validateActivityThreshold } from '@/utils';
import constant from '@/utils/constant';
import format from '@/utils/format';
import dayjs from 'dayjs';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  // 日期改变处理提交数据
  const omApplicationTimeChange = (applicationTime) => {
    console.log(format.formatDateTimeDayjs(applicationTime[0]), format.formatDateTimeDayjs(applicationTime[1]));
    setData({
      applicationTime,
      applicationStartTime: format.formatDateTimeDayjs(applicationTime[0]),
      applicationEndTime: format.formatDateTimeDayjs(applicationTime[1]),
    });
  };
  // 报名时间校验
  const validatorApplicationTime = (rule, val, callback) => {
    if (activityEditDisabled()) {
      callback();
      return;
    }
    const applicationStartTime = val[0];
    const applicationEndTime = val[1];
    if (applicationStartTime && applicationEndTime) {
      const diff = dayjs(applicationEndTime).diff(dayjs(applicationStartTime), 'day');
      if (diff > 60) {
        callback('报名时间间隔不大于60天');
        return;
      }
      if (dayjs(applicationStartTime).valueOf() === dayjs(applicationEndTime).valueOf()) {
        callback('报名开始时间和结束时间不能相同');
        return;
      }
      // 报名时间不早于当前时间
      const isBefore = dayjs(applicationStartTime).isBefore(dayjs());
      if (isBefore) {
        callback('报名开始时间不早于当前时间');
        return;
      }
      // 报名结束时间不得晚于活动的结束时间
      const isAfter = dayjs(applicationEndTime).isAfter(dayjs(value?.endTime));
      if (isAfter) {
        callback('报名结束时间不得晚于活动的结束时间');
      }
      // 报名开始时间不得晚于活动的开始时间
      const isAfterStart = dayjs(applicationStartTime).isAfter(dayjs(value?.startTime));
      if (!isAfterStart) {
        callback('报名开始时间不得早于活动的开始时间');
      }
    } else {
      callback('请选择报名时间');
    }
  };
  return (
    <div>
      <LzPanel title="报名设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="报名时间" required requiredMessage="请选择报名时间" validator={validatorApplicationTime}>
            <RangePicker
              className="w-300"
              name="applicationTime"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={
                formData.receiveRangeData || [
                  new Date(formData.applicationStartTime),
                  new Date(formData.applicationEndTime),
                ]
              }
              onChange={omApplicationTimeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
