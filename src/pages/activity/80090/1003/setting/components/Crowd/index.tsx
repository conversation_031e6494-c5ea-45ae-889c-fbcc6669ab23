import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { Checkbox, Field, Form } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, validateActivityThreshold } from '@/utils';

const FormItem = Form.Item;

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  return (
    <div>
      <LzPanel title="活动人群包生成">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem
            label="参与者生成人群包"
            disabled={false}
            extra={
              <div className="next-form-item-help">
                注：活动结束后将【参与者】生成人群包并保存至【营销中心-云鹿人群管理】
              </div>
            }
          >
            <Checkbox
              checked={formData.crowdPackage}
              onChange={(crowdPackage) => {
                setData({ crowdPackage: crowdPackage ? 1 : 0 });
              }}
            >
              开启
            </Checkbox>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
