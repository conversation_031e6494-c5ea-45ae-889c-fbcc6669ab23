/**
 * 九宫格抽奖数据报表
 */
import React, { useState } from 'react';
import { Button, Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
// import WinRecord from '../record/components/WinRecord';
import FilledRecord from './components/FilledRecord';
import exportCombinedLogs from '@/utils/exportAll';
import { dataWritingLogExport, dataWinningLogExport } from '@/api/v10049';
import { getParams } from '@/utils';
import LzDocGuide from '@/components/LzDocGuide';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  const templateCode = getParams('templateCode');
  return (
    <div className="crm-container">
      <LzPanel title="完善信息有礼数据报表" actions={<LzDocGuide />}>
        {/* <Button */}
        {/*  onClick={() => { */}
        {/*    exportCombinedLogs([dataWritingLogExport, dataWinningLogExport], '完善信息有礼数据报表'); */}
        {/*  }} */}
        {/*  style={{ float: 'right', position: 'relative', zIndex: 1000, marginTop: 8 }} */}
        {/* > */}
        {/*  导出全部 */}
        {/* </Button> */}
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="填写记录" key="1">
            <FilledRecord />
          </Tab.Item>
          {/* {templateCode !== '2001' && ( */}
          {/*  <Tab.Item title="获奖记录" key="2"> */}
          {/*    <WinRecord /> */}
          {/*  </Tab.Item> */}
          {/* )} */}
        </Tab>
      </LzPanel>
    </div>
  );
};
