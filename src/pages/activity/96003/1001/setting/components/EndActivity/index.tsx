/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-02 11:30
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import { Form, Field, Checkbox } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { PageData, FormLayout } from '../../../util';

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>, string) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data }, 'task');
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  return (
    <div>
      <LzPanel title="活动监控">
        <Form {...formItemLayout} field={field}>
          <Form.Item
            label="活动强制结束"
            extra={<div className="next-form-item-help">注：勾选后，所有奖品发放完毕则自动结束活动</div>}
          >
            <Checkbox
              checked={!!formData.endActivity}
              onChange={(endActivity) => setData({ endActivity: endActivity ? 1 : 0 })}
            >
              全部奖品发放完强制结束活动
            </Checkbox>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
