import React, { useState } from 'react';
import { Tab, But<PERSON> } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import WinRecord from './components/WinRecord';
import LotteryRecord from './components/LotteryRecord';
import RankRecord from './components/RankRecord';
import exportCombinedLogs from '@/utils/exportAll';
import { dataJoinLogExport, dataWinningLogExport, dataRankLogExport } from '@/api/v90017';
import LzDocGuide from '@/components/LzDocGuide';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="邀请入会有礼数据报表" actions={<LzDocGuide />}>
        <Button
          onClick={() => {
            exportCombinedLogs([dataJoinLogExport, dataWinningLogExport, dataRankLogExport], '邀请入会有礼数据报表');
          }}
          style={{ float: 'right', position: 'relative', zIndex: 1000, marginTop: 8 }}
        >
          导出全部
        </Button>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="参与记录" key="1">
            <LotteryRecord />
          </Tab.Item>
          <Tab.Item title="中奖记录" key="2">
            <WinRecord />
          </Tab.Item>
          <Tab.Item title="排行榜记录" key="4">
            <RankRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
