import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Select, Field, Table, Button, Message } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataJoinLog, dataJoinLogExport, dataJoinLogUploadPin } from '@/api/v90017';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';
import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';
import styles from '../style.module.scss';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

const IS_WIN = [
  { label: '全部', value: '' },
  { label: '已中奖', value: 1 },
  { label: '未中奖', value: 0 },
];
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  // const onRowSelectionChange = (selectKey: string[]): void => {
  //   console.log(selectKey);
  // };
  // const rowSelection: {
  //   mode: 'single' | 'multiple' | undefined;
  //   onChange: (selectKey: string[]) => void;
  // } = {
  //   mode: 'multiple',
  //   onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  // };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataJoinLog(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataJoinLogExport(formValue).then((data: any) => downloadExcel(data, '参与记录'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);
  const encryptStr = (pin: string): string => {
    const first = pin.slice(0, 1);
    const last = pin.slice(-1);
    return `${first}****${last}`;
  };
  return (
    <div style={{ minHeight: '350px' }}>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="邀请人昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="encryptPin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        {/* <FormItem name="isWin" label="是否中奖" requiredMessage="请选择是否中奖"> */}
        {/*  <Select */}
        {/*    followTrigger */}
        {/*    mode="single" */}
        {/*    showSearch */}
        {/*    hasClear */}
        {/*    style={{ marginRight: 8 }} */}
        {/*    dataSource={IS_WIN} */}
        {/*    defaultValue="" */}
        {/*  /> */}
        {/* </FormItem> */}
        <FormItem name="rangeDate" label="参与时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
        <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}>
          生成人群包
        </Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={80} title="序号" dataIndex="num" />
        <Table.Column
          title="邀请人昵称"
          dataIndex="nickName"
          cell={(value, index, data) => (
            <div className={styles.inARow}>
              <div className={styles.copyText}>{encryptStr(data.nickName)}</div>
              <span
                className={`iconfont icon-fuzhi ${styles.copy}`}
                onClick={() => {
                  Utils.copyText(data.nickName).then(() => {
                    Message.success('邀请人昵称已复制到剪切板');
                  });
                }}
              />
            </div>
          )}
        />
        <Table.Column
          title="助力人昵称"
          dataIndex="assNickName"
          cell={(value, index, data) => (
            <div className={styles.inARow}>
              <div className={styles.copyText}>{encryptStr(data.assNickName)}</div>
              <span
                className={`iconfont icon-fuzhi ${styles.copy}`}
                onClick={() => {
                  Utils.copyText(data.assNickName).then(() => {
                    Message.success('助力人昵称已复制到剪切板');
                  });
                }}
              />
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          dataIndex="encryptPin"
          cell={(value, index, data) => (
            <div className={styles.inARow}>
              <div className={styles.copyText}>{encryptStr(data.encryptPin)}</div>
              <span
                className={`iconfont icon-fuzhi ${styles.copy}`}
                onClick={() => {
                  Utils.copyText(data.encryptPin).then(() => {
                    Message.success('用户pin已复制到剪切板');
                  });
                }}
              />
            </div>
          )}
        />
        <Table.Column
          title="参与时间"
          dataIndex="createTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.createTime)}</div>}
        />
        {/* <Table.Column */}
        {/*  title="是否中奖" */}
        {/*  dataIndex="isWin" */}
        {/*  cell={(value, index, data) => <div>{data.isWin === '1' ? '已中奖' : '未中奖'}</div>} */}
        {/* /> */}
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      <LzDialog
        title="生成人群包"
        className="lz-dialog-mini"
        visible={packVisible}
        footer={false}
        onCancel={() => setPackVisible(false)}
        onClose={() => setPackVisible(false)}
      >
        <LzGenerateCrowdBag
          dataUploadPin={dataJoinLogUploadPin}
          formValue={field.getValues()}
          cancel={() => setPackVisible(false)}
        />
      </LzDialog>
    </div>
  );
};
