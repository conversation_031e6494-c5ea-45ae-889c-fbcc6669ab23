/**
 * Author: z<PERSON>yue
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number | string;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  couponPrizeList: any[];
}
export interface CustomValue {
  actBg: string;
  pageBg: string;
  actBgColor: string;
  shopNameColor: string;
  btnColor: string;
  btnBg: string;
  btnBorderColor: string;
  wheelBg: string;
  wheelPanel: string;
  wheelBtn: string;
  drawBtn: string;
  wheelTextColor: string;
  drawsNum: string;
  winnersBg: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  shopMessageBg: string;
  shopMessageGoToShop: string;
  disableShopName: number;
}
export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  chance: number;
  chanceType: number;
  crowdBag: any;
  totalProbability: number;
  prizeList: PrizeInfo[]; // 根据实际情况，可能需要定义奖品的类型
  taskList: any[]; // 根据实际情况，可能需要定义任务的类型
  winLotteryDayType: number;
  winLotteryDayCounts: number;
  winLotteryTotalType: number;
  winLotteryTotalCounts: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  setting: string;
  prizeListMap: any;
}
interface PrizeType {
  [key: number]: string;
}
export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  btnBg: '',
  btnBorderColor: '',
  // 奖盘背景图
  wheelBg: '',
  wheelPanel: '//img10.360buyimg.com/imgzone/jfs/t1/113191/40/33876/45010/64796380F6595b9a0/819db22bca030ba4.png',
  wheelBtn: '//img10.360buyimg.com/imgzone/jfs/t1/205496/21/15597/29540/61946e89Edad07559/adbeb4ee10b75363.png',
  // 奖盘按钮背景图
  drawBtn: '',
  // 奖盘文案颜色
  wheelTextColor: '',
  // 抽奖次数文案颜色
  drawsNum: '',
  // 获奖名单背景图
  winnersBg: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  shopMessageBg: '',
  shopMessageGoToShop: '',
  disableShopName: 0,
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    shopName: getShop().shopName,
    // 活动名称
    activityName: `大转盘抽奖-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 抽奖机会数
    chance: 0,
    // 初始赠送 1/每人每天赠送 2
    chanceType: 1,
    // 中奖总概率
    totalProbability: 0,
    // 奖品列表
    prizeList: [],
    // 任务列表
    taskList: [],
    // 是否限制每人每天中奖次数 1 不限制 2 限制
    winLotteryDayType: 1,
    // 每人每天中奖次数
    winLotteryDayCounts: 1,
    // 是否限制每人累计次数 1 不限制 2 限制
    winLotteryTotalType: 1,
    // 每人累计中奖次数
    winLotteryTotalCounts: 1,
    // 分享标题
    shareTitle: '幸运大转盘，好礼送不停，快来试手气！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    // 奖品设置 1: 统一设置 2: 按等级设置
    setting: '1',
    // 根据等级设置奖品列表
    prizeListMap: {
      all: [],
    },
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '谢谢参与',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: '',
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  couponPrizeList: [],
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `品牌会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
// eslint-disable-next-line complexity
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  if (prize.prizeType === 1) {
    for (let j = 0; j < prize.couponPrizeList?.length; j++) {
      //   优惠券开始时间
      const couponStart = prize.couponPrizeList[j].startTime;
      const couponEnd = prize.couponPrizeList[j].endTime;
      if (couponStart && couponEnd) {
        const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(couponStart));
        if (isStart) {
          Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

          return false;
        }
        // 奖品的结束时间间是否大于活动的结束时间
        const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(couponEnd));
        if (isEnd) {
          Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
          return false;
        }
      }
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};
// 校验是否设置任务
const hasTask = (formData: Pick<PageData, 'taskList'>): boolean => {
  if (!formData.taskList.length) {
    Message.error('请设置任务');
    return false;
  }
  return true;
};
const hasPrize = (prizeList: PrizeInfo[]): boolean => {
  if (!prizeList.length) {
    Message.error('请设置奖品');
    return false;
  }
  return true;
};

const winCounts = (formData: PageData): boolean => {
  if (
    formData.winLotteryTotalType === 2 &&
    formData.winLotteryDayType === 2 &&
    formData.winLotteryDayCounts > formData.winLotteryTotalCounts
  ) {
    Message.error('“每人每天最多中奖”次数不能大于“每人累计最多中奖”次数');
    return false;
  } else if (formData.winLotteryDayType === 2 && (!formData.winLotteryDayCounts || !formData.winLotteryTotalCounts)) {
    Message.error('请配置中奖限制');
    return false;
  }
  return true;
};
export const checkActivityData = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有设置任务
  if (!hasTask(formData)) {
    return false;
  }
  // 没有选择奖品
  if (!hasPrize(prizeList)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(prizeList, formData)) {
    return false;
  }
  // 中奖次数限制
  if (!winCounts(formData)) {
    return false;
  }
  return true;
};
