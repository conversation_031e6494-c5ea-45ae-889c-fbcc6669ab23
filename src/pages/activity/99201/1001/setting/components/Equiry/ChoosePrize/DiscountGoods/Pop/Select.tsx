import { useEffect, useState, useReducer } from 'react';
import * as React from 'react';
import { Table, Pagination, Dialog, Button } from '@alifd/next';
import SearchForm from './SearchForm';
import format from '@/utils/format';
import { getPromotionPageList } from '@/api/prize';
import CreatePlan from './CreatePlan';
import { IPagePrizePromotionPageResponse } from '@/api/types';
import SkuListDetail from './SkuListDetail';
import styles from './index.module.scss';

interface PageInfo {
  pageSize: number;
  pageNum: number;
  total: number;
}
interface FormData {
  planName: string;
  planId: string;
}

const defaultPage: PageInfo = { pageSize: 10, pageNum: 1, total: 0 };
export default (props) => {
  // const { formData, setData, apply = [0, 1, 2], applyGrade, memberStr, joinTimeLimit = true } = props;
  const { submit, onlyOne, prizeListData } = props;
  // console.log(prizeListData, 'apply===========');
  // 查看SKU折扣详情
  // 查看SKU折扣详情
  const [skuDialog, setSkuDialog] = useReducer((p, c) => {
    return { ...p, ...c };
  }, {});
  const [pageInfo, setPageInfo] = useState<PageInfo>({ ...defaultPage });
  const [searchData, setSearchData] = useState({});
  const [datalist, setList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [planDialog, setPlanDialog] = useState(false);
  const [prizeKeyArr, setPrizeKeyArr] = useState<any[]>([]);
  // 加载列表
  const loadPageList = (page) => {
    setLoading(true);
    getPromotionPageList({ ...searchData, ...page })
      .then((res: IPagePrizePromotionPageResponse) => {
        setList(res.records || []);
        pageInfo.pageNum = res.current as any;
        pageInfo.pageSize = res.size as any;
        pageInfo.total = +res.total!;
        setPageInfo(pageInfo);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 分页事件
  const pageChangeHandler = (pageNum: number) => {
    pageInfo.pageNum = pageNum;
    loadPageList(pageInfo);
  };
  const handlePageSizeChange = (pageSize: number) => {
    pageInfo.pageSize = pageSize;
    pageInfo.pageNum = 1;
    loadPageList(pageInfo);
  };

  // 当选中行的时候
  const onRowSelectionChange = (selectKey: string[]): void => {
    const selectRow = datalist.find((o: any) => selectKey.some((p: any) => p === o.promoId));
    selectRow.prizeType = 5;
    selectRow.unitCount = 1;
    selectRow.dayLimit = 1;
    selectRow.prizeKey = selectRow.promoId;
    // selectRow.perMaxNum = selectRow.perMaxNum;
    // selectRow.perMinNum = selectRow.perMinNum;
    selectRow.prizeName = selectRow.skuInfos[0].skuName;
    selectRow.prizeImg = selectRow.skuInfos[0].skuPicture;
    selectRow.unitPromotional = selectRow.skuInfos[0].promoPrice; // 优惠价
    selectRow.unitPrice = selectRow.skuInfos[0].jingDongPrice;
    selectRow.skuId = selectRow.skuInfos[0].skuId;
    const discount = parseFloat(
      String((selectRow.skuInfos[0].promoPrice / selectRow.skuInfos[0].jingDongPrice) * 10),
    ).toFixed(2);
    selectRow.sendTotalCount = selectRow.quantityRemain;
    selectRow.unitDiscount = parseFloat(discount);
    // console.log(selectRow, 'selectRowselectRow');
    submit(selectRow);
  };

  // 选择器
  const rowSelection: {
    mode: 'single' | 'multiple' | undefined;
    onChange: (selectKey: string[]) => void;
    getProps: (record: any) => { disabled: boolean };
  } = {
    mode: 'single',
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
    getProps: (record) => {
      return {
        disabled: (onlyOne && record.skuNum > 1) || prizeKeyArr.includes(record.promoId),
      };
    },
  };

  // 当行点击的时候
  const onRowClick = (record): void => {
    // 预留，如果要求点击行就选择的话，就解开这行
    // submit(record);
  };
  const onSearch = (formData: FormData): void => {
    setSearchData(formData);
    loadPageList({ ...formData, ...defaultPage });
  };
  const parsePointResListStatus = (status: number): string => {
    switch (status) {
      case 1:
        return '待生效';
      case 2:
        return '已生效';
      default:
        return '';
    }
  };
  const parsePointResListStatusColor = (status: number) => {
    switch (status) {
      case 1:
        return 'table-status-WARNING_COLOR';
      case 2:
        return 'table-status-SUCCESS_COLOR';
      default:
        return 'table-status-LIGHT_GRAY';
    }
  };

  const createPlanSubmit = () => {
    loadPageList(defaultPage);
    setPlanDialog(false);
  };
  const toECardList = () => {
    setPlanDialog(true);
  };

  const openSKUNumDetail = (record) => {
    setSkuDialog({
      visible: true,
      record,
    });
  };
  useEffect(() => {
    // 使用map函数将每个对象的id转换为一个新的字符串数组
    const prizeListData1 = prizeListData.filter((e) => e.prizeType);
    if (prizeListData1 && prizeListData1.length > 0) {
      const idsAsStrings = prizeListData.map((obj) => obj.prizeKey);
      setPrizeKeyArr(idsAsStrings);
    }
  }, []);
  useEffect(() => {
    loadPageList(defaultPage);
  }, []);

  return (
    <div>
      <SearchForm onSearch={onSearch} />
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <div />
        <Button className="table-cell-btn" style={{ marginLeft: '0' }} type="primary" text onClick={toECardList}>
          新建折扣商品 &gt;
        </Button>
      </div>
      <Table.StickyLock
        dataSource={datalist}
        primaryKey="promoId"
        fixedHeader
        maxBodyHeight={500}
        loading={loading}
        onRowClick={onRowClick}
        rowSelection={rowSelection}
      >
        <Table.Column align="left" title="折扣权益名称" dataIndex="planName" />
        <Table.Column
          align="left"
          title="优惠价格"
          cell={(value, index, data) => (
            <div>
              <div>京东价： ¥{data.skuInfos[0].jingDongPrice}</div>
              <div>促销价： ¥{data.skuInfos[0].promoPrice}</div>
            </div>
          )}
        />
        <Table.Column
          width={200}
          align="left"
          title="投放时间"
          cell={(value, index, data) => (
            <div>
              <div>起：{format.formatDateTimeDayjs(new Date(data.beginTime))}</div>
              <div>止：{format.formatDateTimeDayjs(new Date(data.endTime))}</div>
            </div>
          )}
        />
        <Table.Column
          align="left"
          title="SKU数量"
          cell={(value, index, data) => (
            <div>
              <div>{data.skuNum}个</div>
              <div>
                <Button text type="primary" onClick={() => openSKUNumDetail(data)}>
                  查看
                </Button>
              </div>
            </div>
          )}
        />
        {/* <Table.Column align="left" title="库存" cell={(value, index, data) => <div>{data.skuInfos[0].skuStock}</div>} /> */}
        <Table.Column align="left" title="优惠剩余名额" dataIndex="quantityRemain" />
        <Table.Column
          align="left"
          title="时效状态"
          cell={(value, index, data) => (
            <span className={styles[parsePointResListStatusColor(data.planStatus)]}>
              {parsePointResListStatus(data.planStatus)}
            </span>
          )}
        />
      </Table.StickyLock>
      <Pagination
        shape="arrow-only"
        pageSizeSelector="dropdown"
        pageSizePosition="end"
        total={pageInfo.total}
        current={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        pageSizeList={[5, 10, 30, 50, 100]}
        totalRender={(total) => `共${total}条`}
        onPageSizeChange={handlePageSizeChange}
        onChange={pageChangeHandler}
        style={{ marginTop: 10 }}
      />
      <Dialog
        title="新建折扣商品"
        footer={false}
        shouldUpdatePosition
        visible={planDialog}
        onClose={() => setPlanDialog(false)}
      >
        <CreatePlan onlyOne={onlyOne} handleCancel={() => setPlanDialog(false)} handleSubmit={createPlanSubmit} />
      </Dialog>
      <Dialog
        title="查看SKU折扣详情"
        className="lz-dialog-large"
        onClose={() => setSkuDialog({ visible: false })}
        footer={<Button onClick={() => setSkuDialog({ visible: false })}>关闭</Button>}
        visible={skuDialog.visible}
      >
        <SkuListDetail skuData={skuDialog.record} />
      </Dialog>
    </div>
  );
};
