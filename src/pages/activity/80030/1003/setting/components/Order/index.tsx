import React, { useReducer, useImperativeHandle, useEffect, useRef, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form, Grid, Input, NumberPicker, Radio, Select } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, getShopOrderStartTime } from '@/utils';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import styles from '../../style.module.scss';
import BosidengChooseGoods from '@/components/BosidengChooseGoods';
import BosidengSkuImport from '@/components/BosidengSkuImport';
import LzToolTip from '@/components/LzToolTip';
// import SkuImport from './skuImport';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const ORDER_STATUS = [
  { label: '已付款', value: 0 },
  { label: '已完成', value: 1 },
];

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  const BosidengChooseGoodsRef = useRef<{ submit: () => void | null }>(null);
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: dayjs(formData.endTime).subtract(180, 'days').startOf('day').valueOf(),
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
    });
    setTimeout(() => {
      field.validate('orderRestrainRangeData');
    }, 1000);
  }, [formData.endTime]);
  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRestrainRangeData): void => {
    setData({
      orderRestrainRangeData,
      orderRestrainStartTime: format.formatDateTimeDayjs(orderRestrainRangeData[0]),
      orderRestrainEndTime: format.formatDateTimeDayjs(orderRestrainRangeData[1]),
    });
  };
  const handleSkuChange = (data) => {
    // const nerSkuList = deepCopy(formData.orderSkuList);
    setData({ orderSkuList: [...data] });
    console.log('前二十曝光商品信息', [...data]);
    field.setErrors({ orderSkuList: '' });
  };

  const radioDisabled = () => {
    if (formData.orderRestrainStatus === 0) {
      return true;
    } else {
      return false;
    }
  };

  // 下单时间校验
  const validateOrderTime = (rule, val, callback): void => {
    // const { orderRestrainStartTime, orderRestrainEndTime } = formData;
    const orderRestrainStartTime = val[0];
    const orderRestrainEndTime = val[1];
    if (orderRestrainStartTime && orderRestrainEndTime) {
      const diff = dayjs(orderRestrainEndTime).diff(dayjs(orderRestrainStartTime), 'day');
      if (diff > 100) {
        callback('订单验证时间不能超过100天');
      } else {
        callback();
      }
    } else {
      callback('请选下单时间');
    }
  };

  const handleStatusChange = (orderRestrainStatus) => {
    setData({ orderRestrainStatus });
    if (orderRestrainStatus === 0 && formData.isDelayedDisttribution === 1) {
      setData({ orderRestrainStatus: 0, isDelayedDisttribution: 0, awardDays: 0 });
    }
  };

  // 剩余SKU
  const handleSaveSkus = (skuObj) => {
    setData({ surplusSkuObj: { ...formData.surplusSkuObj, ...skuObj } });
  };

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;
      field.validate((errors): void => {
        err = errors;
      });
      const res = BosidengChooseGoodsRef.current?.submit();
      if (!err) {
        err = res;
      }
      return err;
    },
  }));

  return (
    <div>
      <LzPanel title="订单限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="订单限制" disabled={false}>
            <Radio.Group
              value={formData.limitOrder}
              onChange={(limitOrder) => {
                setData({ limitOrder });
              }}
            >
              {/* <Radio value={0}>不限制</Radio> */}
              <Radio value={1}>限制</Radio>
            </Radio.Group>
          </FormItem>
          {!!formData.limitOrder && (
            <>
              <FormItem label="下单时间" required requiredMessage="请选下单时间" validator={validateOrderTime}>
                <RangePicker
                  className="w-300"
                  name="orderRestrainRangeData"
                  inputReadOnly
                  format={dateFormat}
                  hasClear={false}
                  showTime
                  value={
                    formData.orderRestrainRangeData || [
                      new Date(formData.orderRestrainStartTime),
                      new Date(formData.orderRestrainEndTime),
                    ]
                  }
                  onChange={onDataRangeChange}
                  disabledDate={(date) => {
                    return date.valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).startOf('day').valueOf();
                  }}
                />
                <div className={styles.tip}>
                  注：1、默认支持查询
                  {shopOrderInfo.longTermOrder
                    ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后`
                    : `活动结束时间前${shopOrderInfo.orderRetentionDays}天内`}
                  的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理。
                  <br />
                  2、若希望预售消费者参与本次活动，请在设置活动下单时间时包含预售开始时间（注：预售消费者支付定金的时间即为下单时间）。
                </div>
              </FormItem>
              <FormItem
                label="订单状态"
                extra={
                  <div className="next-form-item-help">
                    已付款：用户付款后即可参与活动
                    <br />
                    已完成：(1)用户订单完成后才可参与活动。(2)预售商品需要支付尾款方可参与活动
                  </div>
                }
              >
                <Select dataSource={ORDER_STATUS} value={formData.orderRestrainStatus} onChange={handleStatusChange} />
                <div className={styles.orderTypes}>
                  <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}>
                    已付款
                  </span>
                  <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}>
                    待出库
                  </span>
                  <span className={`${styles.order} ${formData.orderRestrainStatus ? styles.orderGray : ''}`}>
                    待发货
                  </span>
                  <span className={styles.order}>已完成</span>
                </div>
              </FormItem>
              <FormItem label="订单笔数" required>
                <FormItem>
                  <Radio.Group
                    value={formData.orderStrokeCount}
                    onChange={(orderStrokeCount) => setData({ orderStrokeCount })}
                  >
                    <Radio value={1}>单笔</Radio>
                    <Radio value={2}>多笔</Radio>
                  </Radio.Group>
                </FormItem>
                {formData.orderStrokeCount === 2 && (
                  <FormItem required requiredMessage="请输入订单笔数">
                    大于等于{' '}
                    <NumberPicker
                      name="orderStrokeNum"
                      min={1}
                      max={5}
                      type="inline"
                      value={formData.orderStrokeNum}
                      onChange={(orderStrokeNum: number) => setData({ orderStrokeNum })}
                    />{' '}
                    笔
                  </FormItem>
                )}
                <div className={styles.tip}>
                  （1）如设置【单笔】（任意一笔订单）则用户在满足订单规则前提下完成多笔订单，如任意一笔订单金额满足订单金额，则有领奖资格；
                  <br />
                  （2）如设置【多笔】订单，则用户的【符合条件订单数】满足最小订单条件，即可领奖
                </div>
              </FormItem>
              {/* <FormItem label="奖品延迟发放" required> */}
              {/*   <FormItem> */}
              {/*     <Radio.Group */}
              {/*       value={formData.isDelayedDisttribution} */}
              {/*       onChange={(isDelayedDisttribution) => */}
              {/*         setData({ isDelayedDisttribution, awardDays: isDelayedDisttribution }) */}
              {/*       } */}
              {/*     > */}
              {/*       <Radio value={0}>否</Radio> */}
              {/*       <Radio value={1} disabled={radioDisabled()}> */}
              {/*         是 */}
              {/*       </Radio> */}
              {/*     </Radio.Group> */}
              {/*   </FormItem> */}
              {/*   {formData.isDelayedDisttribution === 1 && formData.orderRestrainStatus === 1 && ( */}
              {/*     <FormItem required requiredMessage="请输入延迟天数"> */}
              {/*       延迟发放{' '} */}
              {/*       <NumberPicker */}
              {/*         name="awardDays" */}
              {/*         min={1} */}
              {/*         max={99} */}
              {/*         type="inline" */}
              {/*         value={formData.awardDays} */}
              {/*         onChange={(awardDays: number) => setData({ awardDays })} */}
              {/*       />{' '} */}
              {/*       天 */}
              {/*     </FormItem> */}
              {/*   )} */}
              {/* </FormItem> */}
              <FormItem label="订单拆单后是否合并" required>
                <RadioGroup
                  value={formData.split}
                  onChange={(split: number) => {
                    setData({ split });
                  }}
                >
                  <Radio id="1" value={1}>
                    是
                  </Radio>
                  <Radio id="0" value={0}>
                    否
                  </Radio>
                </RadioGroup>
                <LzToolTip
                  content={
                    <div>
                      <div>
                        <p>拆单：因平台因素，用户的某笔总的父订单会有被拆单成多笔子订单发货的情况。</p>
                        <br />
                        当用户订单在平台上被拆分为多笔子订单时，可以选择以下方式来判断活动参与门槛： <br />
                        • 选择“是”：按照父订单（将本店铺的子订单合并后的订单）来判断参与门槛。 <br />
                        • 选择“否”：按照拆单后的子订单（包括父订单）来判断参与门槛。 <br />
                      </div>
                    </div>
                  }
                />
              </FormItem>
              <FormItem label="价格类型">
                <Radio.Group value={formData.priceType} onChange={(val) => setData({ priceType: val })}>
                  <Radio value={0}>京东价</Radio>
                  <Radio value={1}>实付价 (POP店为实付价，自营店为京东价)</Radio>
                </Radio.Group>
              </FormItem>
              <FormItem
                label="订单金额"
                required
                requiredMessage="请输入订单金额"
                extra={<div className="next-form-item-help">注：若选择指定商品，则订单中指定商品金额需满足所设值</div>}
              >
                大于等于{' '}
                <NumberPicker
                  precision={2}
                  name="orderRestrainAmount"
                  min={0}
                  max={9999999}
                  type="inline"
                  value={formData.orderRestrainAmount}
                  onChange={(orderRestrainAmount: number) => setData({ orderRestrainAmount })}
                />{' '}
                元
              </FormItem>
              <FormItem label="订单商品" required>
                <RadioGroup
                  value={formData.orderSkuisExposure}
                  onChange={(orderSkuisExposure: number) => {
                    if (!orderSkuisExposure) {
                      formData.orderSkuList = [];
                      formData.surplusSkuObj = {};
                    }
                    setData({
                      orderSkuisExposure,
                      orderSkuList: formData.orderSkuList,
                      surplusSkuObj: formData.surplusSkuObj,
                    });
                  }}
                >
                  <Radio id="0" value={0}>
                    全部商品
                  </Radio>
                  <Radio id="1" value={1}>
                    指定商品
                  </Radio>
                </RadioGroup>
                <Grid.Row>
                  {formData.orderSkuisExposure === 1 && (
                    <FormItem
                      name="orderSkuList"
                      required
                      requiredMessage={'请选择订单商品'}
                      style={{ marginTop: '15px' }}
                    >
                      <div>前20个sku：</div>
                      <BosidengChooseGoods
                        value={formData.orderSkuList}
                        onChange={handleSkuChange}
                        max={500}
                        isOrder
                        sRef={BosidengChooseGoodsRef}
                      />
                      <Input className="validateInput" name="orderSkuList" value={formData.orderSkuList} />
                    </FormItem>
                  )}
                </Grid.Row>
                <Grid.Row>
                  {formData.orderSkuisExposure === 1 && (
                    <FormItem
                      name="surplusSkuObj"
                      // required
                      // requiredMessage={'请选择剩余订单商品'}
                      style={{ marginTop: '15px' }}
                    >
                      <div>剩余SKU：</div>
                      <BosidengSkuImport
                        handleSaveSkus={handleSaveSkus}
                        surplusSkuObj={formData.surplusSkuObj}
                        isOrder
                      />
                      <p className={styles.tip}>
                        注：优先按店铺顺序，再按照skuid填入的顺序，展示前20个上传的sku；剩余商品随机排序
                      </p>
                      <Input className="validateInput" name="surplusSkuObj" value={formData.surplusSkuObj} />
                    </FormItem>
                  )}
                </Grid.Row>
              </FormItem>
            </>
          )}
        </Form>
      </LzPanel>
    </div>
  );
};
