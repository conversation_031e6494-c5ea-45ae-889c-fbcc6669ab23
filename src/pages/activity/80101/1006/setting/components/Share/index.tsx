/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-06 15:33
 * Description:
 */
import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import { Form, Field, Input, Grid, Button } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzImageSelector from '@/components/LzImageSelector';
import styles from '../../style.module.scss';

import { CustomValue, FormLayout, PageData } from '@/pages/activity/80101/1005/util';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
  decoValue: CustomValue | undefined;
}
export default ({ onChange, defaultValue, value, sRef, decoValue }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const field: Field = Field.useField();
  useEffect((): void => {
    const data: PageData = value || defaultValue;
    data.mpImg = value?.mpImg || decoValue!.mpImg;
    data.h5Img = value?.h5Img || decoValue!.h5Img;
    data.cmdImg = value?.cmdImg || decoValue!.cmdImg;
    setFormData(data);
  }, [value]);
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  return (
    <div>
      <LzPanel title="分享设置">
        <Form {...formItemLayout} field={field}>
          <FormItem label="图文分享标题" required requiredMessage="请输入分享标题">
            <Input
              placeholder="请输入分享标题"
              value={formData.shareTitle}
              name="shareTitle"
              maxLength={25}
              showLimitHint
              className="w-300"
              onChange={(shareTitle) => setData({ shareTitle })}
            />
          </FormItem>
          <Form.Item label="图文分享图片" required>
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={120}
                  height={120}
                  value={formData.h5Img || decoValue?.h5Img}
                  onChange={(h5Img) => {
                    setData({ h5Img });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tips}>
                  <p>图片尺寸：120px*120px</p>
                  <p>图片大小：不超过32KB</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setData({ h5Img: decoValue?.h5Img });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="京口令分享图片" required>
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={582}
                  height={320}
                  value={formData.cmdImg || decoValue?.cmdImg}
                  onChange={(cmdImg: string) => {
                    setData({ cmdImg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tips}>
                  <p>图片尺寸：582px*320px</p>
                  <p>图片大小：不超过80KB</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setData({ cmdImg: decoValue?.cmdImg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="小程序分享图片" required>
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  value={formData.mpImg || decoValue?.mpImg}
                  onChange={(mpImg: string) => {
                    setData({ mpImg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tips}>
                  <p>图片尺寸：建议使用5:4图</p>
                  <p>图片大小：不超过128KB</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setData({ mpImg: decoValue?.mpImg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
