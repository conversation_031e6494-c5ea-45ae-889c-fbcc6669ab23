import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, Field, Radio, Input } from '@alifd/next';
import { activityEditDisabled, calcDateDiff, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import format from '@/utils/format';

const FormItem = Form.Item;

const RadioGroup = Radio.Group;

const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    setData({
      rangeDate,
      startTime: format.formatDateTimeDayjs(rangeDate[0]),
      endTime: format.formatDateTimeDayjs(rangeDate[1]),
    });
  };
  // 获取持续时间
  const DateRangeDuration = ({ rangeDate }) => {
    const duration: string = calcDateDiff(rangeDate);
    return (
      <span style={{ fontSize: '12px', marginLeft: '5px' }}>
        {
          <span>
            活动持续<span style={{ color: 'red' }}>{duration || '不足1小时'}</span>
          </span>
        }
      </span>
    );
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel
        title="权益类型配置"
        subTitle={<div style={{ color: 'red' }}>注意：此处配置活动创建后将无法修改，配置时请仔细检查</div>}
      >
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="新客礼1.0配置" required>
            <RadioGroup
              value={formData.newCustomerOneRightsType}
              onChange={(newCustomerOneRightsType) => setData({ newCustomerOneRightsType })}
              name="newCustomerOneRightsType"
            >
              <Radio value={1}>新客礼1.1（全店商品）</Radio>
              <Radio value={2}>新客礼1.2（指定商品）</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="老客礼1.0配置" required>
            <RadioGroup
              value={formData.oldCustomerOneRightsType}
              onChange={(oldCustomerOneRightsType) => setData({ oldCustomerOneRightsType })}
              name="oldCustomerOneRightsType"
            >
              <Radio value={1}>老客礼1.1（全店商品）</Radio>
              <Radio value={2}>老客礼1.2（指定商品）</Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
