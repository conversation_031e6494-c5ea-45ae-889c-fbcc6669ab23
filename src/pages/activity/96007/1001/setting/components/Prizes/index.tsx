import React, {useReducer, useEffect, useImperativeHandle, useState} from 'react';
import {Form, Field, Table, Button, Dialog} from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import {FormLayout, PageData, PRIZE_INFO, PRIZE_TYPE, PrizeInfo} from '../../../util';
import {getParams} from "@/utils";
import LzDialog from "@/components/LzDialog";
import ChoosePrize from "@/components/ChoosePrizeForDZ";

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();
  // 当前操作类型
  const operationType: string = getParams('type');
  const isEdit: boolean = operationType === 'edit';
  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 是否刚刚添加了一个新奖品
  const [hasAddNewPrize, setHasAddNewPrize] = useState(false);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.prizeList[target] = data;
    formData.prizeList[target].status = 1;
    setData(formData);
    setVisible(false);
  };

  const onCancel = (): void => {
    setVisible(false);
  };

  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  /**
   * 上架新奖品，下架上一个启用奖品
   */
  const setNewPrizeInfo = () => {
    const num = formData.prizeList.length;
    formData.prizeList[num - 1].status = 0;
    formData.prizeList.push(PRIZE_INFO);
    setHasAddNewPrize(true)
    setTarget(num);
    setVisible(true);
  };

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  useEffect(() => {
    if (getParams('type') === 'tpl') {
      setData({ prizeList: [PRIZE_INFO]})
    }
  }, []);

  return (
    <div>
      <LzPanel title="奖品设置">
        <Form {...formItemLayout} field={field}>
          <FormItem label="奖品列表" required>
            <Table dataSource={formData.prizeList}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              />
               <Table.Column
                 title="奖品状态"
                 cell={(_, index, row) => (
                   <div>
                     {row && row.prizeName === '' ? '-' : (row && row.status === 1 ? '已启用' : '下架')}
                   </div>
                 )}
               />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
                <Table.Column
                  title="操作"
                  width={130}
                  cell={(val, index, _) => (
                    // <FormItem style={{ paddingTop: '18px' }} disabled={!(hasAddNewPrize || (index === target && index !== 0))}>
                    <FormItem style={{ paddingTop: '18px' }} disabled={!(index === target && index !== 0) && getParams('type') === 'tpl'}>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          let row = formData.prizeList[index];
                          if (row.prizeName === '') {
                            setEditValue(null);
                          } else {
                            setEditValue(row);
                          }
                          setTarget(index);
                          setVisible(true);
                        }}
                      >
                        <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                      </Button>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          if (_.prizeType) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认清空该奖品？',
                              onOk: () => {
                                formData.prizeList.splice(index, 1, PRIZE_INFO);
                                formData.totalProbability = formData.prizeList
                                  .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '')
                                  .reduce((v, total) => {
                                    return v + Number(total.probability);
                                  }, 0);
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }
                        }}
                      >
                        <i className={`iconfont icon-shanchu`} />
                      </Button>
                    </FormItem>
                  )}
                />
            </Table>
            {
              isEdit && (
                <Button
                  disabled={hasAddNewPrize}
                  type="secondary"
                  style={{ marginTop: 10 }}
                  onClick={() => setNewPrizeInfo()}
                >
                  添加新奖品并下架上一个启用奖品
                </Button>
              )
            }
          </FormItem>
        </Form>
      </LzPanel>
      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasLimit={false}
          typeList={[2,3,4,6,7]}
          defaultTarget={2}
          hasProbability={false}
          defaultEditValue={null}
        />
      </LzDialog>
    </div>
  );
};
