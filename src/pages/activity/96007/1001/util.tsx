import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  status: number;
}

export interface CustomValue {
  actBg: string,
  actBgColor: string,
  shopNameColor: string,
  ruleBtn: string,
  myPrizeBtn: string,
  myOrderBtn: string,
  step1Bg: string,
  skuItemBg: string,
  step2Bg: string,
  step3Bg: string,
  step4Bg: string,
  disableShopName: number,
  cmdImg: string,
  h5Img: string,
  mpImg: string,
}

export interface PageData {
  shopName: string;
  // 活动名称
  activityName: string;
  // 日期区间（不提交）
  rangeDate: Dayjs[];
  // 活动开始时间
  startTime: string;
  // 活动结束时间
  endTime: string;
  // 活动门槛（不提交）
  threshold: number;
  // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  // 评价时间
  evaluateTime: Dayjs[];
  // 评价等级
  evaluateGrade: number;
  // 评价字数是否限制
  evaluateNumberType: number;
  // 评价字数
  evaluateNumber: number;
  // 评价图片是否限制
  evaluateImgType: number;
  // 评价图片数量
  evaluateImg: number;
  //  评价视频是否限制
  evaluateVideoType: number;
  // 评价视频数量
  evaluateVideo: number;
  // 过滤文字是否限制
  evaluateFilterateType: number;
  // 过滤文字
  evaluateFilterate: string;
  // 奖品列表
  prizeList: PrizeInfo[];
  // 分享标题
  shareStatus: number;
  shareTitle: string;
  // 分享图片
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  // 活动规则
  rules: string;
  templateCode: string;
  gradeLabel: string[];
  crowd: any;
  crowdBag: any;
  setting: string;
  // 评价开始时间
  evaluateStartTime: string;
  // 评价结束时间
  evaluateEndTime: string;
}

export interface EvaluateSkuList {
  skuId: string;
  skuMainPicture: string;
  skuName: string;
  imgType: string;
  nameType: string;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  actBg: '',
  actBgColor: '',
  shopNameColor: '',
  ruleBtn: '',
  myPrizeBtn: '',
  myOrderBtn: '',
  step1Bg: '',
  skuItemBg: '',
  step2Bg: '',
  step3Bg: '',
  step4Bg: '',
  disableShopName: 0,
  cmdImg: '',
  h5Img: '',
  mpImg: '',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): {
  evaluateTime: any[];
  crowd: undefined;
  evaluateNumber: number;
  shopName: any;
  threshold: number;
  rules: string;
  disableShopName: number;
  evaluateVideo: number;
  templateCode: string;
  setting: string;
  evaluateImgType: number;
  mpImg: string;
  prizeList: any[];
  startTime: any;
  evaluateVideoType: number;
  crowdBag: null;
  limitJoinTimeType: number;
  rangeDate: any[];
  gradeLabel: any[];
  joinTimeRange: any;
  evaluateGrade: number;
  evaluateFilterateType: number;
  evaluateSkuList: any[];
  h5Img: string;
  winLotteryTotalCounts: number;
  activityName: string;
  supportLevels: string;
  evaluateFilterate: string;
  evaluateWordType: number;
  evaluateWord: any[];
  evaluateEndTime: any;
  evaluateNumberType: number;
  joinStartTime: string;
  shareTitle: string;
  joinEndTime: string;
  cmdImg: string;
  evaluateImg: number;
  endTime: any;
  isEvaluateSku: number;
  evaluateStartTime: any;
  shareStatus: number;
} => {
  return {
    winLotteryTotalCounts: 0,
    shopName: getShop().shopName,
    crowd: undefined,
    // 活动名称
    activityName: `评价有礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5,-9',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 评价时间
    evaluateTime: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 评价开始时间
    evaluateStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 评价结束时间
    evaluateEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 评价等级
    evaluateGrade: 1,
    // 评价字数是否限制
    evaluateNumberType: 1,
    // 评价字数
    evaluateNumber: 1,
    // 评价图片是否限制
    evaluateImgType: 1,
    // 评价图片数量
    evaluateImg: 1,
    //  评价视频是否限制
    evaluateVideoType: 1,
    // 评价视频数量
    evaluateVideo: 1,
    // 过滤文字是否限制
    evaluateFilterateType: 1,
    // 过滤文字
    evaluateFilterate: '',
    // 关键字是否限制1不限制2限制
    evaluateWordType: 1,
    // 关键字
    evaluateWord: [''],
    // 评价sku
    evaluateSkuList: [],
    isEvaluateSku: 1,
    // 奖品列表
    prizeList: [],
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '评价有礼，好礼送不停！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    // 奖品设置 1: 统一设置 2: 按等级设置
    setting: '1',
    disableShopName: 0
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  // 奖品状态 1-已启用 0-下架
  status: 1,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

export const evaluateGradeList = [
  { label: '一星及以上', value: 1 },
  { label: '二星及以上', value: 2 },
  { label: '三星及以上', value: 3 },
  { label: '四星及以上', value: 4 },
  { label: '五星', value: 5 },
];

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};

const hasPrize = (prizeList: PrizeInfo[]): boolean => {
  if (!prizeList.length) {
    Message.error('请设置奖品');
    return false;
  }
  return true;
};

// 校验评价规则
const isEvaluateValid = (formData: PageData): boolean => {
  // 评价时间
  if (dayjs(formData.endTime).isBefore(dayjs(formData.evaluateEndTime))) {
    Message.error('评价结束时间应小于活动结束时间');
    return false;
  }
  return true;
};

export const checkActivityData = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 评价规则
  if (!isEvaluateValid(formData)) {
    return false;
  }
  // 没有选择奖品
  if (!hasPrize(prizeList)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(prizeList, formData)) {
    return false;
  }
  return true;
};
