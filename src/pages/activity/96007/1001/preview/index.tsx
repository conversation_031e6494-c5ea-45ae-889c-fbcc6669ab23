import React, { useReducer } from 'react';
import { Form, Table, Input } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE, evaluateGradeList } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

// eslint-disable-next-line complexity
export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="评价时间">
          {format.formatDateTimeDayjs(formData.evaluateTime[0])}至{format.formatDateTimeDayjs(formData.evaluateTime[1])}
        </FormItem>
        <FormItem label="评价等级">
          {evaluateGradeList.find((item) => item.value === formData.evaluateGrade)?.label}
        </FormItem>
        <FormItem label="评价字数">
          {formData.evaluateNumberType === 1 && '不限制'}
          {formData.evaluateNumberType === 2 && `${formData.evaluateNumber}`}
        </FormItem>
        <FormItem label="评价图片数量">
          {formData.evaluateImgType === 1 && '不限制'}
          {formData.evaluateImgType === 2 && `${formData.evaluateImg}`}
        </FormItem>
        <FormItem label="评价视频">
          {formData.evaluateVideoType === 1 && '不限制'}
          {formData.evaluateVideoType === 2 && '评论必须包含一个以上视频'}
        </FormItem>
        <FormItem label="负面词过滤">
          {formData.evaluateFilterateType === 1 && '不限制'}
          {formData.evaluateFilterateType === 2 && `${formData.evaluateFilterate}`}
        </FormItem>
        <FormItem label="关键字是否限制">
          {formData.evaluateWordType === 1 && '不限制'}
          {formData.evaluateWordType === 2 && `${formData.evaluateWord}`}
        </FormItem>
        <FormItem label="评价商品">
          {formData.evaluateSkuList && formData.evaluateSkuList.length > 0 && (
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '16px',
              marginTop: '16px'
            }}>

              {formData.evaluateSkuList.map((item, index) => (
                <div key={index} style={{
                  width: 'calc(33.333% - 11px)',
                  minWidth: '280px',
                  border: '1px solid #e6e6e6',
                  borderRadius: '8px',
                  padding: '12px',
                  backgroundColor: '#fafafa',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '8px'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px'
                  }}>
                    <img
                      src={item.skuMainPicture}
                      alt=""
                      style={{
                        width: '60px',
                        objectFit: 'contain',
                        borderRadius: '4px',
                        border: '1px solid #ddd'
                      }}
                    />
                    <div style={{ flex: 1 }}>
                      <div style={{
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#333',
                        marginBottom: '4px',
                        lineHeight: '1.4'
                      }}>
                        {item.skuName}
                      </div>
                      <div style={{
                        fontSize: '12px',
                        color: '#666',
                        fontFamily: 'monospace'
                      }}>
                        skuId：{item.skuId}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </FormItem>

        <FormItem label="奖品列表" isPreview={false}>
          <Table
            dataSource={formData.prizeList.filter((e) => e.prizeName !== '谢谢参与')}
            style={{ marginTop: '15px' }}
          >
            <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
            <Table.Column title="奖项名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(_, index, row) => (
                <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                  {PRIZE_TYPE[row.prizeType]}
                </div>
              )}
            />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                if (row.prizeType === 1) {
                  return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                } else {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }
              }}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
            />
            <Table.Column
              title="奖品状态"
              cell={(_, index, row) => (
                <div>
                  {row && row.prizeName === '' ? '-' : (row && row.status === 1 ? '已启用' : '下架')}
                </div>
              )}
            />
            <Table.Column
              title="奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
          </Table>
        </FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
