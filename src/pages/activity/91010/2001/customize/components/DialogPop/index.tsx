/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 基础元素
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 6,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}
export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel title="弹窗">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="入会">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={573}
                  height={611}
                  value={formData.joinVipPopBg}
                  onChange={(joinVipPopBg) => {
                    setForm({ joinVipPopBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度573px*611px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ joinVipPopBg: defaultValue?.joinVipPopBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="审核结果">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={501}
                  height={451}
                  value={formData.reviewResultPop}
                  onChange={(reviewResultPop) => {
                    setForm({ reviewResultPop });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度501px*451px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ reviewResultPop: defaultValue?.reviewResultPop });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="我的奖品">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={501}
                  height={451}
                  value={formData.myPrizePop}
                  onChange={(myPrizePop) => {
                    setForm({ myPrizePop });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度501px*451px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ myPrizePop: defaultValue?.myPrizePop });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="填写/查看档案">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={630}
                  height={1002}
                  value={formData.infoPop}
                  onChange={(infoPop) => {
                    setForm({ infoPop });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度630px*1002px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ infoPop: defaultValue?.infoPop });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="上传图片/视频">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={479}
                  height={713}
                  value={formData.upLoadPop}
                  onChange={(upLoadPop) => {
                    setForm({ upLoadPop });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度479px*713px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ upLoadPop: defaultValue?.upLoadPop });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>

          <Form.Item label="提交信息提示">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={479}
                  height={391}
                  value={formData.submitTipPopBg}
                  onChange={(submitTipPopBg) => {
                    setForm({ submitTipPopBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度479px*391px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ submitTipPopBg: defaultValue?.submitTipPopBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          {/*<Form.Item label="提交信息成功">*/}
          {/*  <Grid.Row>*/}
          {/*    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>*/}
          {/*      <LzImageSelector*/}
          {/*        width={479}*/}
          {/*        height={391}*/}
          {/*        value={formData.submitSuccPopBg}*/}
          {/*        onChange={(submitSuccPopBg) => {*/}
          {/*          setForm({ submitSuccPopBg });*/}
          {/*        }}*/}
          {/*      />*/}
          {/*    </Form.Item>*/}
          {/*    <Form.Item style={{ marginBottom: 0 }}>*/}
          {/*      <div className={styles.tip}>*/}
          {/*        <p>图片尺寸：宽度479px*391px</p>*/}
          {/*        <p>图片大小：不超过1M</p>*/}
          {/*        <p>图片格式：JPG、JPEG、PNG、GIF</p>*/}
          {/*      </div>*/}
          {/*      <div>*/}
          {/*        <Button*/}
          {/*          type="primary"*/}
          {/*          text*/}
          {/*          onClick={() => {*/}
          {/*            setForm({ submitSuccPopBg: defaultValue?.submitSuccPopBg });*/}
          {/*          }}*/}
          {/*        >*/}
          {/*          重置*/}
          {/*        </Button>*/}
          {/*      </div>*/}
          {/*    </Form.Item>*/}
          {/*  </Grid.Row>*/}
          {/*</Form.Item>*/}
          <Form.Item label="图片审核成功">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={479}
                  height={391}
                  value={formData.reviewSuccPopBg}
                  onChange={(reviewSuccPopBg) => {
                    setForm({ reviewSuccPopBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度479px*391px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ reviewSuccPopBg: defaultValue?.reviewSuccPopBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="不符合新客身份">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={479}
                  height={391}
                  value={formData.submitFailPopBg}
                  onChange={(submitFailPopBg) => {
                    setForm({ submitFailPopBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度479px*391px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ submitFailPopBg: defaultValue?.submitFailPopBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="动态文案背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={479}
                  height={391}
                  value={formData.otherPopBg}
                  onChange={(otherPopBg) => {
                    setForm({ otherPopBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度479px*391px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ otherPopBg: defaultValue?.otherPopBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
