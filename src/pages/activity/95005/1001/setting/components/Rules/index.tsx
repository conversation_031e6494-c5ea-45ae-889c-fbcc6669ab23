import React, { useImperativeHandle, useReducer, useEffect } from 'react';
import { Form, Field, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData } from '../../../util';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
  checkForm: () => boolean;
}

export default ({ onChange, defaultValue, value, sRef, checkForm }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // const generatePrizeString = () => {
  //   const prizeList = formData.prizeList.filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '');
  //   const strList = prizeList.map((item, index): string => {
  //     return `（${index + 1}） ${formData.signType === 1 ? '累计' : '连续'}签到${item.signDay}天，有机会获得${
  //       item.prizeName
  //     }，单份奖品价值${Number(item.unitPrice).toFixed(2)}元`;
  //   });
  //   return strList.join('\n');
  // };

  // const getMaxNum = () => {
  //   if (formData.receiveLimit === 0) {
  //     return '不限制';
  //   } else {
  //     return ` 参与用户最多可领取${formData.maxReceive}次奖励`;
  //   }
  // };

  /**
   * 自动生成规则说明
   */
  //   const autoCreateRuleDesc = async (): Promise<void> => {
  //     if (!checkForm()) return;
  //     const isValidateData: boolean = checkActivityData(formData);
  //     if (!isValidateData) {
  //       return;
  //     }
  //     const rules = `1、活动时间：${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
  //       formData.endTime,
  //     )}；
  // 2、活动对象：${generateMembershipString(formData, 'string')}；
  // 3、签到时间：${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(formData.signEndTime)}；
  // 4、领奖时间：${format.formatDateTimeDayjs(formData.receiveStartTime)}至${format.formatDateTimeDayjs(formData.endTime)}；
  // 5、符合累计签到赠送条件，则有机会获得累计签到奖品；
  // ${generatePrizeString()}
  // 6、签到奖励可领取次数：${getMaxNum()}；
  // 7、签到成功后有机会获得奖品，也有可能会与奖品擦肩而过（奖品数量有限，先到先得）；
  // 8、若用户存在刷奖等恶意行为，一经发现将取消抽奖资格（如奖品已经发放，有权追回奖品）；
  // 注：奖品发放形式：
  // 实物奖：
  // （1）中奖客户信息收集：页面弹屏提示中奖客户提供收货地址、号码和奖项；（超过1小时未填写对应收货信息，视为放弃）
  // （2）实物奖采取寄送方式发放，获奖用户需在开奖后填写姓名、联系电话、详细地址信息。如因用户原因无法联系上，即奖品作废不再补发，或如用户未填写真实有效的信息或填写收货信息不详，均视为放弃奖品；
  // 虚拟产品奖：
  // （1）京豆、红包、京东E卡、积分：中奖后系统预计24小时内自动发放至账户；
  // （2）优惠券：中奖后自动发放，预计24小时到账；
  // （3）PLUS会员、爱奇艺会员卡、网易云音乐、优酷视频、喜马拉雅、樊登读书、酷狗音乐等月卡，年卡中奖后需手动兑换；
  // 【活动参与主体资格】
  // （1）每位自然人用户仅能使用一个京东账号参与活动，WX号、QQ、京东账号、手机号码、收货地址等任一信息一致或指向同一用户的，视为同一个用户，则第一个参与本活动的账号参与结果有效，其余账号参与结果均视为无效。
  // （2）若发现同一位用户使用不同账号重复参与活动，承办方有权取消其参与资格。
  // 【注意事项】
  // （1）活动过程中，凡以不正当手段（如作弊领取、恶意套取、刷信誉、虚假交易、扰乱系统、实施网络攻击等）参与本次活动的用户，商家有权终止其参与活动，并取消其参与资格（如优惠已发放，商家有权追回），如给商家造成损失的，商家将保留向违规用户继续追索的权利。
  // （2）如遇不可抗力（包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动中存在大面积作弊行为、活动遭受严重网络攻击或因系统故障导致活动中奖名单大批量出错，活动不能正常进行的），商家有权取消、修改或暂停本活动。
  // （3）是否获得优惠以活动发布者后台统计结果为准。
  // `;
  //     setData({ rules });
  //     field.setErrors({ rules: '' });
  //   };
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={2000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
          {/* <FormItem label=" " colon={false}> */}
          {/*  <Button */}
          {/*    type="primary" */}
          {/*    className="table-cell-btn" */}
          {/*    onClick={autoCreateRuleDesc} */}
          {/*    style={{ marginRight: '15px' }} */}
          {/*    text */}
          {/*  > */}
          {/*    自动生成规则说明 */}
          {/*  </Button> */}
          {/*  <span style={{ color: 'red', fontSize: '12px' }}> */}
          {/*    提示：点击按钮自动生成规则，也可以自己编辑信息，手机端规则处显示。 */}
          {/*  </span> */}
          {/* </FormItem> */}
        </Form>
      </LzPanel>
    </div>
  );
};
