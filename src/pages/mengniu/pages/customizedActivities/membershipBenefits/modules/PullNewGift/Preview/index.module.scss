.pullnew-gift {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 6.66em * 1.05;
  height: 6.24em * 1.05;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  margin-top: 0.2em;
  margin-left: 0.35em;

  .pullnew-rule-button {
    position: absolute;
    top: 0.6em;
    right: 0.27em;
    width: 1.11em;
    height: 0.455em;
  }

  .pullnew-share-button {
    position: absolute;
    top: 0.75em;
    left: 0.07em;
    width: 1.11em;
    height: 0.455em;
  }

  .pullnew-record-button {
    position: absolute;
    top: 1.15em;
    right: 0.27em;
    width: 1.11em;
    height: 0.455em;
  }

  .prize-container {
    // position: absolute;
    // top: 1.4em;
    display: flex;
    flex-direction: column;
    align-content: center;
  }

  .prize-list {
    position: absolute;
    top: 1.7em;
    left: 50%;
    width: 95%;
    margin: 0 !important;
    transform: translateX(-51%);

    .prize-slide {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    :global(.swiper-wrapper) {
      align-items: center;
    }

    :global(.swiper-slide-next) {
      .prize-bg {
        width: 2.27em;
        height: 3.47em;
        .level {
          line-height: 0.45em !important;
          display: flex;
          margin-top: 0.1em;
          span {
            font-size: 0.25em !important;
          }
        }

        .prize-name {
          // font-size: 0.26em !important;
          line-height: 0.3em !important;
          margin-top: 0.15em !important;
          display: flex;
          span {
            max-width: 88%;
            font-size: 0.23em !important;
          }
        }

        .prize-img {
          width: 1.35em !important;
          height: 1.35em !important;
        }

        .get-button {
          width: 1.8em !important;
          height: 0.52em !important;
        }
      }
    }

    .prize-bg {
      width: 1.96em;
      height: 2.99em;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% 100%;
      // background-color: skyblue;

      .prize-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        // width: 95%;
        // height: 94%;

        .level {
          width: 95%;
          line-height: 0.4em;
          position: relative;
          text-align: center;
          display: flex;
          justify-content: center;
          margin-top: 0.05em;
          span {
            font-size: 0.22em;
          }
        }

        .prize-name {
          width: 95%;
          position: relative;
          text-align: center;
          height: 0.3em;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 0.1em;
          span {
            font-size: 0.2em;
            max-width: 88%;
          }
        }

        .prize-img {
          position: relative;
          margin-top: 0;
          width: 1.2em;
          height: 1.2em;
        }

        .get-button {
          width: 1.54em;
          height: 0.45em;
          background-repeat: no-repeat;
          background-position: center;
          background-size: 100% 100%;
          position: relative;
          margin-top: 0em;
        }
      }
    }
  }

  .prize-list-2 {
    top: 6.1em;
  }

  .prize-list-3 {
    top: 10.4em;
  }

  .share-button {
    position: absolute;
    bottom: 1em;
    width: 2.47em;
    height: 0.68em;
  }

  .oneBottom {
    bottom: 0.7em;
  }
}
