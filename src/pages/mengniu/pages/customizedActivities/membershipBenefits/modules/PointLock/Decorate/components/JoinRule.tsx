import ChoosePrize from '@/components/ChoosePrize';
import LzDialog from '@/components/LzDialog';
import LzPanel from '@/components/LzPanel';
import LzTipPanel from '@/components/LzTipPanel';
import MChooseSku from '@/pages/mengniu/components/MChooseSku';
import { formItemLayout } from '@/pages/mengniu/public/scripts/commonSetting';
import decorateStyles from '@/pages/mengniu/public/styles/decorate.module.scss';
import { PRIZE_INFO } from '@/pages/mengniu/utils';
import constant from '@/utils/constant';
import { Button, Card, Dialog, Form, Input, Message, NumberPicker, Table } from '@alifd/next';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import cloneDeep from 'lodash/cloneDeep';
import React, { useState } from 'react';

dayjs.extend(isBetween);

const { PRIZE_TYPE } = constant;

export default function JoinRule({ data, resetData, updateCurrentModuleData, field, state }) {
  const {
    score,
    activityData,
    activityData: { prizeGroupList },
    showStartTime,
    showEndTime,
    activityStartTime,
    activityEndTime,
    skuList = [],
  } = data;

  const { prizeList = [] } = prizeGroupList[0] || {};
  const prizeInfoItem = prizeList[0];

  // 选择奖品
  const [visible, setVisible] = useState(false);

  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);

  // 当前编辑行索引
  const [editIndex, setEditIndex] = useState(0);

  // 更新单个活动的数据
  function updatePrizeList(index, updateObj) {
    const newList = cloneDeep(prizeList);
    newList[index] = {
      ...newList[index],
      ...updateObj,
    };
    newList.forEach((item, idx) => {
      item.sortId = idx + 1;
      item.orderBy = idx + 1;
    });
    updateCurrentModuleData_activityData({
      prizeList: newList,
    });
  }

  function updateCurrentModuleData_activityData(obj) {
    updateCurrentModuleData({
      activityData: {
        ...activityData,
        prizeGroupList: [
          {
            ...activityData.prizeGroupList[0],
            ...obj,
          },
        ],
      },
    });
  }

  const onPrizeChange = (index, choosePrize) => {
    updatePrizeList(index, {
      ...choosePrize,
      prizeInfoJson: choosePrize,
    });

    setEditValue(null);
    setEditIndex(0);
    setVisible(false);
  };

  const onCancel = (): void => {
    setEditValue(null);
    setEditIndex(0);
    setVisible(false);
  };

  const onSkuTableChange = (prevSkus, updateSkus) => {
    const newSkus = updateSkus.map((item) => {
      const prevSku = prevSkus.find((item2) => item2.skuId === item.skuId);
      if (prevSku) {
        if (prevSku.skuImg == item.skuImg) {
          return prevSku;
        } else {
          return item;
        }
      } else {
        return {
          ...item,
          skuImg: item.skuMainPicture,
        };
      }
    });

    updateCurrentModuleData({
      skuList: newSkus.map((sku, skuIndex) => ({
        ...sku,
        orderBy: skuIndex + 1,
      })),
    });
  };

  const validatePrize = (rule, value, callback) => {
    const list = JSON.parse(value);
    if (list.length === 0) {
      return callback(`请完善奖品设置`);
    }
    if (list.filter((item) => item.prizeType === 0).length > 0) {
      return callback(`有未设置的奖品`);
    }
    if (list.filter((item) => item.exchangeNum === undefined).length > 0) {
      return callback(`领奖条件必填`);
    }
    if (list.filter((item) => item.exchangeNum === 0).length > 0) {
      return callback(`领奖条件不能为0元`);
    }
    return callback();
  };

  const validateSkuList = (rule, value, callback) => {
    if (value < 1) {
      return callback(`活动商品不能小于1个`);
    }
    return callback();
  };

  // 活动内修改奖品，给出提示
  const saveWarn = () => {
    return new Promise<void>((resolve, reject) => {
      const isWithinActivityTime = dayjs().isBetween(dayjs(activityStartTime), dayjs(activityEndTime), null, '[]');
      if (isWithinActivityTime) {
        Dialog.warning({
          title: '提示',
          content: '当前活动进行中，修改奖品可能导致正在领取的用户领取状态异常，有客诉风险，确认继续保存修改吗？',
          onOk: () => {
            resolve();
          },
          onCancel: () => {
            reject();
          },
        });
      } else {
        resolve();
      }
    });
  };

  return (
    <Card free className={decorateStyles['card-container']}>
      <Card.Header
        className={decorateStyles['card-header']}
        title={
          <>
            <span className="crm-label">参与规则</span>
          </>
        }
      />
      <Card.Content>
        <LzPanel>
          <LzTipPanel
            warning
            message="注意：订单金额为京东价，活动奖品领取门槛为订单完成（确认收货），下单时间在活动时间内，完成时间在投放时间结束前，即可领取奖品"
          />
          <Form field={field} {...formItemLayout}>
            <Form.Item
              name={`prizeInfo`}
              label="奖励设置"
              required
              requiredMessage={`请完善奖品设置`}
              validator={(rule, value, callback) => validatePrize(rule, value, callback)}
            >
              <Input htmlType="hidden" value={JSON.stringify(prizeList)} />
              {(() => {
                const dataSource = [prizeInfoItem];
                if (dataSource.length === 0) {
                  dataSource.push(PRIZE_INFO);
                }
                return (
                  <Table.StickyLock dataSource={dataSource}>
                    <Table.Column
                      width={240}
                      title="领奖条件"
                      dataIndex="exchangeNum"
                      cell={(_, index, row) => (
                        <div>
                          <NumberPicker
                            style={{ width: '100%' }}
                            type="inline"
                            min={1}
                            max={999999}
                            step={1}
                            value={row.exchangeNum}
                            label="累计消费"
                            innerAfter={'元'}
                            onChange={(value) => {
                              updatePrizeList(index, {
                                exchangeNum: value,
                              });
                              setTimeout(() => {
                                field.validate([`prizeInfo`]);
                              }, 20);
                            }}
                          />
                        </div>
                      )}
                    />
                    <Table.Column title="奖项名称" dataIndex="prizeName" />
                    <Table.Column
                      title="奖项类型"
                      dataIndex="prizeType"
                      cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                    />
                    <Table.Column
                      title="单位数量"
                      cell={(_, index, row) => <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>}
                    />
                    <Table.Column
                      title="单份价值(元)"
                      cell={(_, index, row) => (
                        <>{row.prizeType != 0 && <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0}</div>}</>
                      )}
                    />
                    <Table.Column
                      title="发放份数"
                      cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                    />
                    <Table.Column
                      title="操作"
                      width={80}
                      lock="right"
                      cell={(val, o_index, _) => (
                        <>
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              try {
                                let rowPrizeInfo = {
                                  ...prizeInfoItem.prizeInfoJson,
                                  ...prizeInfoItem,
                                };
                                if (rowPrizeInfo.prizeName === '谢谢参与') {
                                  rowPrizeInfo = null;
                                }
                                setEditValue(rowPrizeInfo);
                                setEditIndex(o_index);
                                setVisible(true);
                              } catch (error) {
                                setEditValue(null);
                                setEditIndex(0);
                                setVisible(true);
                              }
                            }}
                          >
                            <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                          </Button>
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              if (_.prizeType) {
                                Dialog.confirm({
                                  v2: true,
                                  title: '提示',
                                  centered: true,
                                  content: '确认清空该奖品？',
                                  onOk: () => {
                                    updatePrizeList(o_index, {
                                      ...PRIZE_INFO,
                                      prizeId: '',
                                    });
                                  },
                                } as any);
                              }
                            }}
                          >
                            <i className={`iconfont icon-shanchu`} />
                          </Button>
                        </>
                      )}
                    />
                  </Table.StickyLock>
                );
              })()}
              <LzDialog
                title={false}
                visible={visible}
                footer={false}
                onClose={() => setVisible(false)}
                style={{ width: '670px' }}
              >
                <ChoosePrize
                  initTarget={2}
                  typeList={[1, 2, 3, 7]}
                  editValue={editValue}
                  hasProbability={false}
                  onChange={async (choosePrize) => {
                    await saveWarn();
                    // 计划开始时间
                    const startTime = choosePrize.startTime || choosePrize.startDate;
                    // 计划结束时间
                    const endTime = choosePrize.endTime || choosePrize.endDate;
                    if (startTime && endTime) {
                      const errorMsg: string[] = [];
                      if (dayjs(activityStartTime).isBefore(dayjs(startTime))) {
                        errorMsg.push('奖品生效时间，应该早于活动开始时间');
                      }
                      if (dayjs(activityEndTime).isAfter(dayjs(endTime))) {
                        errorMsg.push('奖品失效时间，应该晚于活动结束时间');
                      }
                      if (errorMsg.length) {
                        const errorMessageDom = (
                          <div>
                            {errorMsg.map((item, index) => (
                              <p key={index}>{item}</p>
                            ))}
                          </div>
                        );
                        return Message.error(errorMessageDom);
                      }
                    }
                    onPrizeChange(editIndex, choosePrize);
                    setTimeout(() => {
                      field.validate([`prizeInfo`]);
                    }, 20);
                  }}
                  onCancel={onCancel}
                />
              </LzDialog>
            </Form.Item>
            <Form.Item
              label="兑换所需积分"
              name={`needPoint`}
              min={1}
              max={999999}
              required
              requiredMessage={`请设置兑换所需积分`}
            >
              <NumberPicker
                style={{ width: '220px' }}
                type="inline"
                min={1}
                max={999999}
                step={1}
                value={score}
                placeholder="请输入兑换所需积分"
                innerAfter={'积分'}
                onChange={(value) => {
                  updateCurrentModuleData({
                    score: value,
                  });
                }}
              />
            </Form.Item>
            <Form.Item
              name={`skuInfo`}
              label="指定商品"
              required
              validator={(rule, value, callback) => validateSkuList(rule, value, callback)}
            >
              <Input htmlType="hidden" value={skuList.length} />
              <MChooseSku
                value={skuList}
                onChange={(skus) => {
                  onSkuTableChange(skuList, skus);
                  setTimeout(() => {
                    field.validate([`skuInfo`]);
                  }, 20);
                }}
              />
            </Form.Item>
          </Form>
        </LzPanel>
      </Card.Content>
    </Card>
  );
}
