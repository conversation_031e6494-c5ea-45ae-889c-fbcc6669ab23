import classNames from 'classnames';
import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import styles from './index.module.scss';

export default function OrderReceiveGiftsPreview({ data, state, dispatch }) {
  if (!data) return null;

  const { jsonData, activityData } = data;
  const prizeGroupList = activityData?.prizeGroupList || [];
  const { prizeList = [] } = prizeGroupList[0] || {};

  return (
    <div className={styles['order-receive-gifts']}>
      <div className={styles['order-receive-gifts-title']} style={{ backgroundImage: `url(${jsonData.titleImg}})` }} />
      {/* 活动列表 */}
      <div
        className={styles['order-activity-container']}
        style={{ backgroundImage: `url(${jsonData.orderActivityBgImg}})` }}
      >
        <Swiper>
          {prizeList.map((slide, index) => (
            <SwiperSlide className={styles['activity-slide']} key={index}>
              <div className={styles['activity-item']}>
                {/* 倒计时 */}
                <div
                  className={styles['activity-count-down']}
                  style={{
                    color: `${jsonData.countDownColor}`,
                    borderColor: `${jsonData.countDownColor}`,
                  }}
                >
                  <span>活动结束倒计时：</span>
                  <span>00天00时00分</span>
                </div>
                {/* 活动规则按钮 */}
                <img
                  src={state.modules.KvSwiper?.jsonData?.ruleButtonImg}
                  className={styles['rule-button']}
                  onClick={(e) => {
                    if (state.selectedModule != 'OrderReceiveGifts') return;
                    dispatch({
                      type: 'SHOW_POPUP',
                      payload: {
                        popupName: 'RulePopup',
                        popupData: {
                          rule: slide.rule,
                        },
                      },
                    });
                  }}
                />
                {/* 领奖记录按钮 */}
                <img
                  src={state.modules.KvSwiper?.jsonData?.prizeRecordButtonImg}
                  className={styles['prize-record-button']}
                  onClick={(e) => {
                    if (state.selectedModule != 'OrderReceiveGifts') return;
                    dispatch({
                      type: 'SHOW_POPUP',
                      payload: {
                        popupName: 'MyPrizePopup',
                        popupData: {},
                      },
                    });
                  }}
                />
                {/* 奖品展示 */}
                <div className={styles['gift-img']} style={{ backgroundImage: `url(${slide.showImg}})` }}>
                  <span
                    className={styles['receive-button']}
                    onClick={(e) => {
                      if (state.selectedModule != 'OrderReceiveGifts') return;
                      dispatch({
                        type: 'SHOW_POPUP',
                        payload: {
                          popupName: 'DrawResPopup',
                          popupData: {},
                        },
                      });
                    }}
                  />
                </div>
                {/* sku列表 */}
                <div className={classNames(styles['sku-list'], 'no-scrollbar')}>
                  {slide.orderSkuList.map((skuImgItem, skuIndex) => (
                    <div className={styles['sku-img-item']} key={skuIndex}>
                      <img src={skuImgItem.skuImg} className={styles['sku-img']} />
                    </div>
                  ))}
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
