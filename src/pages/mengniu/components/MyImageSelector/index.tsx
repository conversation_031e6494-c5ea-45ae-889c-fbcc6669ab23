import LzImageSelector from '@/components/LzImageSelector';
import decorateStyles from '@/pages/mengniu/public/styles/decorate.module.scss';
import { Box, Button } from '@alifd/next';
import React, { useEffect, useRef } from 'react';

export default ({ value, onChange, onReset, width, height }) => {
  const resetValue = useRef('');

  useEffect(() => {
    resetValue.current = value;
  }, []);

  return (
    <Box direction="row">
      <Box align="center">
        <LzImageSelector width={width} height={height} value={value} onChange={onChange} />
        <Button
          text
          type="primary"
          className={decorateStyles['reset-button']}
          onClick={() => {
            onReset(resetValue.current);
          }}
        >
          重置
        </Button>
      </Box>
    </Box>
  );
};
