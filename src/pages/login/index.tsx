import React from 'react';
import { Form, Input } from '@alifd/next';
import styles from './style.module.scss';
import constant from '@/utils/constant';
import { login } from '@/api/test';

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: {
    fixedSpan: 6,
  },
  wrapperCol: {
    span: 14,
  },
  colon: true,
};

export default ({ history }) => {
  const handleSubmit = async (values, errors) => {
    if (errors) {
      return;
    }
    localStorage.setItem(constant.LZ_SSO_PRD, values.prd);
    const token = await login({ shopId: values.shopId });
    localStorage.setItem(constant.LZ_SSO_TOKEN, token);
    localStorage.setItem(
      constant.LZ_CURRENT_SHOP,
      JSON.stringify({
        isDefault: true,
        venderId: 739130,
        shopName: '广博长泽专卖店',
        shopId: 734259,
        venderType: 0,
        logoUrl: 'https://img30.360buyimg.com/popshop/jfs/t12040/167/1277706166/88856/87939c85/5a1e9842N3d659b8f.jpg',
      }),
    );
    localStorage.setItem(
      constant.LZ_CRM_BIZ_USER,
      JSON.stringify({
        username: 'ZYChangZeTest',
      }),
    );
    // 旧版 1 平台专业版 2 数据旗舰版
    // 新版 在localStorage LZ_CURRENT_SHOP 5 平台专业版 6 数据旗舰版
    sessionStorage.setItem(
      'shopInfoYB',
      JSON.stringify({
        version: '2',
      }),
    );
    history.replace('/');
  };
  return (
    <div className={styles.loginPage}>
      <Form className={styles.loginForm} {...formItemLayout} colon>
        <FormItem name="prd" label="Prd" required requiredMessage="Please input your Prd!">
          <Input placeholder="Please Enter Prd" defaultValue="crm" />
        </FormItem>
        <FormItem name="shopId" label="shopId" required requiredMessage="Please input your shopId!">
          <Input placeholder="Please Enter shopId" defaultValue="734259" />
        </FormItem>
        <FormItem label=" " colon={false}>
          <Form.Submit type="primary" validate onClick={handleSubmit} style={{ marginRight: 8 }}>
            Submit
          </Form.Submit>
          <Form.Reset>Reset</Form.Reset>
        </FormItem>
      </Form>
    </div>
  );
};
