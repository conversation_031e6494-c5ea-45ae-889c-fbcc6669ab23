import popSettingDecorate from '@/pages/swisse/interestCenter/notJoinPage/PopSetting/modules/PopSetting';


export const initialState = () => {
  return {
    selectedModule: 'popSetting',
    previews: {
      popSetting: {
        decorateComponent: popSettingDecorate,
        name: '弹窗设置',
      }
    },
    modules: {
      popSetting: {
        pop1ThresholdStart: 0,
        pop1ThresholdEnd: 0,
        pop1LinkUrl: '',
        pop2ThresholdStart: 0,
        pop2ThresholdEnd: 0,
        pop2LinkUrl: '',
        pop3ThresholdStart: 0,
        pop3ThresholdEnd: 0,
        pop3LinkUrl: '',
      },
    },
    pushAllowed: {
      popSetting: 0,
    },
  };
};
function updateModule(modules, selectedModule, payload) {
  if (Array.isArray(modules[selectedModule])) {
    return {
      ...modules,
      [selectedModule]: payload,
    };
  } else {
    return {
      ...modules,
      [selectedModule]: {
        ...modules[selectedModule],
        ...payload,
      },
    };
  }
}
function updatePushAllowed(pushAllowed, selectedModule) {
  return {
    ...pushAllowed,
    [selectedModule]: pushAllowed[selectedModule] + 1,
  };
}
function resetPushAllowed(pushAllowed, selectedModule) {
  return {
    ...pushAllowed,
    [selectedModule]: 0,
  };
}

export function decorateReducer(state, action) {
  switch (action.type) {
    case 'SELECT_MODULE':
      return { ...state, selectedModule: action.payload };
    case 'UPDATE_MODULE':
      return {
        ...state,
        modules: updateModule(state.modules, state.selectedModule, action.payload),
        pushAllowed: updatePushAllowed(state.pushAllowed, state.selectedModule),
      };
    case 'RESET_PUSH': {
      return {
        ...state,
        pushAllowed: resetPushAllowed(state.pushAllowed, state.selectedModule),
      };
    }
    case 'INIT_MODULE': {
      return {
        ...state,
        modules: {
          ...state.modules,
          ...action.payload,
        },
      };
    }
    default:
      return state;
  }
}
