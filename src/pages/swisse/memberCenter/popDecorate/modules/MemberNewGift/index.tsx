import { popJsonSaveZphyJson } from '@/api/zphy';
import LzImageSelector from '@/components/LzImageSelector';
import { Button, Card, Divider, Field, Form, Input, Message, Tab } from '@alifd/next';
import React from 'react';
import styles from './index.module.scss';

function MemberNewGift({ data, allJson, dispatch, defaultData, activityActiveTab }) {
  console.log('MemberNewGift', data, defaultData);
  const [pageLoading, setPageLoading] = React.useState(false);
  const field = Field.useField({});
  const disableLength = data.showZhuoRui ? 4 : 8;
  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };

  const addItem = () => {
    // if (data.biActivity[activeKey].list.length < 3) {
    const obj = {
      giftImg: '',
      skuId: '',
    };
    if (data.showZhuoRui) {
      data.tabs[`tab${activityActiveTab.newGiftTab}`].push(obj);
      setData({ tabs: data.tabs });
    } else {
      data.swiperList.push(obj);
      setData({ swiperList: data.swiperList });
    }
    // }
  };
  const changeKey = (key) => {
    dispatch({ type: 'UPDATE_ACTIVITYACTIVETAB', payload: { biTab: '1', newGiftTab: key } });
  };
  const isValidMemberNewGift = (activityList) => {
    for (const activity of activityList) {
      if (!activity.giftImg) {
        return false;
      }
      if (!activity.skuId) {
        return false;
      }
    }
    return true;
  };
  const saveSetting = (): any => {
    if (!data.titleImg) {
      Message.error('请上传标题图片');
      return false;
    }
    if (allJson.showZhuoRui) {
      if (!data.tabs.tab1_title || !data.tabs.tab2_title) {
        Message.error('请填写按钮文案');
        return false;
      }
      if (!isValidMemberNewGift(data.tabs.tab1)) {
        Message.error('请检查图片是否上传或者sku是否配置');
        return false;
      }
      if (!isValidMemberNewGift(data.tabs.tab2)) {
        Message.error('请检查图片是否上传或者sku是否配置');
        return false;
      }
    } else if (!isValidMemberNewGift(data.swiperList)) {
      Message.error('请检查图片是否上传或者sku是否配置');
      return false;
    }

    let updateJson = {};
    if (allJson.showZhuoRui) {
      updateJson = {
        ...allJson,
        ZRNewMember: {
          ...allJson.ZRNewMember,
          MemberNewGift: { ...data },
        },
        ZROldMember: {
          ...allJson.ZROldMember,
          MemberNewGift: { ...data },
        },
      };
    } else {
      updateJson = {
        ...allJson,
        NewMember: {
          ...allJson.NewMember,
          MemberNewGift: { ...data },
        },
        OldMember: {
          ...allJson.OldMember,
          MemberNewGift: { ...data },
        },
      };
    }
    console.log('updateJson', updateJson);
    const params = {
      json: JSON.stringify(updateJson),
      type: 1,
      version: allJson.version,
    };
    setPageLoading(true);
    popJsonSaveZphyJson(params)
      .then((res) => {
        Message.success('保存成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };

  const commonProps = {
    title: '新客专享价',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <div className={styles.operation}>
            <div>人群限制：{data.showZhuoRui ? '仅卓睿新客可见' : '仅新客可见'}</div>
            <div className={styles.imgUpload}>
              <div>标题图片：</div>
              <div>
                <LzImageSelector
                  width={429}
                  height={44}
                  value={data.titleImg}
                  onChange={(titleImg) => {
                    setData({ titleImg });
                  }}
                />
                <Button
                  style={{ marginLeft: '35px' }}
                  type={'primary'}
                  text
                  onClick={() => {
                    setData({ titleImg: defaultData.MemberNewGift.titleImg });
                  }}
                >
                  重置
                </Button>
              </div>
              <div className={styles.tip}>
                <div>图片尺寸：为429*44px,支持jpg、jpeg、png格式，大小不超过1M</div>
              </div>
            </div>
            {data.showZhuoRui ? (
              <>
                <Tab activeKey={activityActiveTab.newGiftTab} onChange={(key) => changeKey(key)}>
                  <Tab.Item title="按钮1编辑" key="1" />
                  <Tab.Item title="按钮2编辑" key="2" />
                </Tab>
                <div className={styles.operation}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div>按钮文案：</div>
                    <Input
                      name="tips"
                      style={{ width: '300px' }}
                      value={data.tabs[`tab${activityActiveTab.newGiftTab}_title`]}
                      onChange={(v) => {
                        data.tabs[`tab${activityActiveTab.newGiftTab}_title`] = v;
                        setData({ tabs: data.tabs });
                      }}
                    />
                  </div>
                  <div className={styles.exchangeContainer}>
                    <div>
                      <Button
                        type={'primary'}
                        onClick={() => addItem()}
                        disabled={data.tabs[`tab${activityActiveTab.newGiftTab}`].length >= 4}
                      >
                        添加奖品（{data.tabs[`tab${activityActiveTab.newGiftTab}`].length}/4）
                      </Button>
                      <span className={'tip'} style={{ marginLeft: 5 }}>
                        注：最多可添加4个奖品
                      </span>
                    </div>

                    {data.tabs[`tab${activityActiveTab.newGiftTab}`].map((item, index) => {
                      return (
                        <div key={index}>
                          <div className={styles.itemContainer}>
                            <div className={styles.itemImg}>
                              <LzImageSelector
                                width={750}
                                value={item.giftImg}
                                onChange={(giftImg) => {
                                  item.giftImg = giftImg;
                                  setData({ tabs: data.tabs });
                                }}
                              />
                              <Button
                                style={{ marginLeft: '35px' }}
                                type={'primary'}
                                text
                                onClick={() => {
                                  data.tabs[`tab${activityActiveTab.newGiftTab}`][index].giftImg =
                                    defaultData.MemberNewGift.tabs[`tab${activityActiveTab.newGiftTab}`][index].giftImg;
                                  setData({ tabs: data.tabs });
                                }}
                              >
                                重置
                              </Button>
                            </div>
                            <div className={styles.itemInfo}>
                              <div className={styles.tip}>
                                <div>图片尺寸：仅支持宽度为750px，高度不超过2000px</div>
                                <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                              </div>
                              <div style={{ display: 'flex', alignItems: 'baseline', marginTop: '10px' }}>
                                <div style={{ width: '100px' }}>配置sku:</div>
                                <Form style={{ width: '100%' }} field={field}>
                                  <Form.Item required name={`skuId${index}`} requiredMessage={'请输入配置sku'}>
                                    <Input
                                      style={{ width: '300px' }}
                                      name={`skuId${index}`}
                                      value={item.skuId}
                                      placeholder={'请输入配置sku'}
                                      onFocus={() => {}}
                                      onChange={(val) => {
                                        const numericValue = val.replace(/\D/g, '');
                                        data.tabs[`tab${activityActiveTab.newGiftTab}`][index].skuId = numericValue;
                                        setData({ tabs: data.tabs });
                                      }}
                                    />
                                  </Form.Item>
                                </Form>
                              </div>
                              <div className={styles.icons}>
                                {/* 置顶 */}
                                <Button
                                  text
                                  type={'primary'}
                                  disabled={!index}
                                  onClick={() => {
                                    const temp = data.tabs[`tab${activityActiveTab.newGiftTab}`][index];
                                    data.tabs[`tab${activityActiveTab.newGiftTab}`][index] =
                                      data.tabs[`tab${activityActiveTab.newGiftTab}`][0];
                                    data.tabs[`tab${activityActiveTab.newGiftTab}`][0] = temp;
                                    setData({ tabs: data.tabs });
                                  }}
                                >
                                  <i className={['iconfont', 'icon-iconxx-31', styles.transform180].join(' ')} />
                                </Button>
                                {/* 上移 */}
                                <Button
                                  text
                                  type={'primary'}
                                  disabled={!index}
                                  onClick={() => {
                                    const temp = data.tabs[`tab${activityActiveTab.newGiftTab}`][index];
                                    data.tabs[`tab${activityActiveTab.newGiftTab}`][index] =
                                      data.tabs[`tab${activityActiveTab.newGiftTab}`][index - 1];
                                    data.tabs[`tab${activityActiveTab.newGiftTab}`][index - 1] = temp;
                                    setData({ tabs: data.tabs });
                                  }}
                                >
                                  <i className="iconfont icon-iconjiantou-35" />
                                </Button>

                                {/* 下移 */}
                                <Button
                                  text
                                  disabled={index === data.tabs[`tab${activityActiveTab.newGiftTab}`].length - 1}
                                  type={'primary'}
                                  onClick={() => {
                                    const temp = data.tabs[`tab${activityActiveTab.newGiftTab}`][index];
                                    data.tabs[`tab${activityActiveTab.newGiftTab}`][index] =
                                      data.tabs[`tab${activityActiveTab.newGiftTab}`][index + 1];
                                    data.tabs[`tab${activityActiveTab.newGiftTab}`][index + 1] = temp;
                                    setData({ tabs: data.tabs });
                                  }}
                                >
                                  <i className="iconfont icon-iconjiantou-34" />
                                </Button>

                                {/* 置底 */}
                                <Button
                                  text
                                  disabled={index === data.tabs[`tab${activityActiveTab.newGiftTab}`].length - 1}
                                  onClick={() => {
                                    const temp = data.tabs[`tab${activityActiveTab.newGiftTab}`][index];
                                    data.tabs[`tab${activityActiveTab.newGiftTab}`][index] =
                                      data.tabs[`tab${activityActiveTab.newGiftTab}`][
                                        data.tabs[`tab${activityActiveTab.newGiftTab}`].length - 1
                                      ];
                                    data.tabs[`tab${activityActiveTab.newGiftTab}`][
                                      data.tabs[`tab${activityActiveTab.newGiftTab}`].length - 1
                                    ] = temp;
                                    setData({ tabs: data.tabs });
                                  }}
                                  type={'primary'}
                                >
                                  <i className="iconfont icon-iconxx-31" />
                                </Button>
                                {/* 删除 */}
                                <Button type={'primary'} text>
                                  <i
                                    className="iconfont icon-icon-07 btn-del"
                                    onClick={() => {
                                      data.tabs[`tab${activityActiveTab.newGiftTab}`].splice(index, 1);
                                      setData({ tabs: data.tabs });
                                    }}
                                  />
                                </Button>
                              </div>
                            </div>
                          </div>
                          <Divider />
                        </div>
                      );
                    })}
                  </div>
                </div>
              </>
            ) : (
              <div className={styles.exchangeContainer}>
                <div>
                  <Button type={'primary'} onClick={() => addItem()} disabled={data.swiperList.length >= disableLength}>
                    添加奖品（{data.swiperList.length}/{disableLength}）
                  </Button>
                  <span className={'tip'} style={{ marginLeft: 5 }}>
                    注：最多可添加{disableLength}个奖品
                  </span>
                </div>

                {data.swiperList.map((item, index) => {
                  return (
                    <div key={index}>
                      <div className={styles.itemContainer}>
                        <div className={styles.itemImg}>
                          <LzImageSelector
                            width={359}
                            height={350}
                            value={item.giftImg}
                            onChange={(giftImg) => {
                              item.giftImg = giftImg;
                              setData({ swiperList: data.swiperList });
                            }}
                          />
                          <Button
                            style={{ marginLeft: '35px' }}
                            type={'primary'}
                            text
                            onClick={() => {
                              data.swiperList[index].giftImg = defaultData.MemberNewGift.swiperList[index].giftImg;
                              setData({ swiperList: data.swiperList });
                            }}
                          >
                            重置
                          </Button>
                        </div>
                        <div className={styles.itemInfo}>
                          <div className={styles.tip}>
                            <div>图片尺寸：为359*350px,支持jpg、jpeg、png格式，大小不超过1M</div>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'baseline', marginTop: '10px' }}>
                            <div style={{ width: '100px' }}>配置sku:</div>
                            <Form style={{ width: '100%' }} field={field}>
                              <Form.Item required name={`skuId${index}`} requiredMessage={'请输入配置sku'}>
                                <Input
                                  style={{ width: '300px' }}
                                  name={`skuId${index}`}
                                  value={item.skuId}
                                  placeholder={'请输入配置sku'}
                                  onFocus={() => {}}
                                  onChange={(val) => {
                                    const numericValue = val.replace(/\D/g, '');
                                    data.swiperList[index].skuId = numericValue;
                                    setData({ swiperList: data.swiperList });
                                  }}
                                />
                              </Form.Item>
                            </Form>
                          </div>
                          <div className={styles.icons}>
                            {/* 置顶 */}
                            <Button
                              text
                              type={'primary'}
                              disabled={!index}
                              onClick={() => {
                                const temp = data.swiperList[index];
                                data.swiperList[index] = data.swiperList[0];
                                data.swiperList[0] = temp;
                                setData({ swiperList: data.swiperList });
                              }}
                            >
                              <i className={['iconfont', 'icon-iconxx-31', styles.transform180].join(' ')} />
                            </Button>
                            {/* 上移 */}
                            <Button
                              text
                              type={'primary'}
                              disabled={!index}
                              onClick={() => {
                                const temp = data.swiperList[index];
                                data.swiperList[index] = data.swiperList[index - 1];
                                data.swiperList[index - 1] = temp;
                                setData({ swiperList: data.swiperList });
                              }}
                            >
                              <i className="iconfont icon-iconjiantou-35" />
                            </Button>

                            {/* 下移 */}
                            <Button
                              text
                              disabled={index === data.swiperList.length - 1}
                              type={'primary'}
                              onClick={() => {
                                const temp = data.swiperList[index];
                                data.swiperList[index] = data.swiperList[index + 1];
                                data.swiperList[index + 1] = temp;
                                setData({ swiperList: data.swiperList });
                              }}
                            >
                              <i className="iconfont icon-iconjiantou-34" />
                            </Button>

                            {/* 置底 */}
                            <Button
                              text
                              disabled={index === data.swiperList.length - 1}
                              onClick={() => {
                                const temp = data.swiperList[index];
                                data.swiperList[index] = data.swiperList[data.swiperList.length - 1];
                                data.swiperList[data.swiperList.length - 1] = temp;
                                setData({ swiperList: data.swiperList });
                              }}
                              type={'primary'}
                            >
                              <i className="iconfont icon-iconxx-31" />
                            </Button>
                            {/* 删除 */}
                            <Button type={'primary'} text>
                              <i
                                className="iconfont icon-icon-07 btn-del"
                                onClick={() => {
                                  data.swiperList.splice(index, 1);
                                  setData({ swiperList: data.swiperList });
                                }}
                              />
                            </Button>
                          </div>
                        </div>
                      </div>
                      <Divider />
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}

export default MemberNewGift;
